#!/usr/bin/env python3
"""
Simple example showing how to fix the base64 data URI error.

This demonstrates the exact fix for the error:
"Error decoding base64: FormatException: Invalid character (at character 5)"
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pydantic import BaseModel, ValidationError
from pydantic.types import Base64Str  # This causes the error

# Import the fix
from src.infrastructure.utils.pydantic_types import DataUriBase64String
from src.infrastructure.utils.data_uri_utils import extract_base64_from_data_uri


def demonstrate_problem():
    """Show the original problem."""
    print("🔴 ORIGINAL PROBLEM:")
    print("=" * 50)
    
    # This is the type of data URI that causes the error
    data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    class OriginalModel(BaseModel):
        image: Base64Str  # This will fail
    
    try:
        model = OriginalModel(image=data_uri)
        print("✅ Worked (unexpected)")
    except ValidationError as e:
        print(f"❌ Failed with error: {e}")
        print("   This is the 'Invalid character (at character 5)' error!")


def demonstrate_fix_option_1():
    """Show fix option 1: Use new Pydantic type."""
    print("\n🟢 FIX OPTION 1: Use DataUriBase64String")
    print("=" * 50)
    
    data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    class FixedModel(BaseModel):
        image: DataUriBase64String  # This works with data URIs!
    
    try:
        model = FixedModel(image=data_uri)
        print("✅ Success! Model created successfully")
        print(f"   Image data: {model.image[:20]}...")
        print(f"   Serialized: {model.model_dump()}")
    except ValidationError as e:
        print(f"❌ Failed: {e}")


def demonstrate_fix_option_2():
    """Show fix option 2: Pre-process the data URI."""
    print("\n🟢 FIX OPTION 2: Pre-process data URI")
    print("=" * 50)
    
    data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    # Extract just the base64 content
    base64_content = extract_base64_from_data_uri(data_uri)
    
    if base64_content:
        print(f"✅ Extracted base64: {base64_content[:20]}...")
        
        # Now you can use it with the original Pydantic type
        class OriginalModel(BaseModel):
            image: Base64Str
        
        try:
            model = OriginalModel(image=base64_content)
            print("✅ Success! Original model works with extracted base64")
        except ValidationError as e:
            print(f"❌ Failed: {e}")
    else:
        print("❌ Failed to extract base64 content")


def main():
    """Run the demonstration."""
    print("BASE64 DATA URI ERROR FIX")
    print("This shows how to fix the 'Invalid character (at character 5)' error")
    print()
    
    demonstrate_problem()
    demonstrate_fix_option_1()
    demonstrate_fix_option_2()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("✅ Option 1: Replace Base64Str with DataUriBase64String")
    print("✅ Option 2: Pre-process data URIs to extract base64 content")
    print("✅ Both options handle the data URI prefix properly")
    print("✅ Your 'Invalid character (at character 5)' error is now fixed!")


if __name__ == "__main__":
    main()
