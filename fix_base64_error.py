#!/usr/bin/env python3
"""
PERBAIKAN LANGSUNG UNTUK ERROR BASE64 DATA URI

Script ini akan memperbaiki error "Invalid character (at character 5)" 
dengan mengganti semua penggunaan Base64Str dan Base64Bytes di proyek Anda.
"""

import os
import sys
import re
from pathlib import Path

def fix_base64_imports_in_file(file_path: Path) -> bool:
    """Perbaiki import Base64 di file Python."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern untuk mencari import Base64Str dan Base64Bytes
        patterns_to_fix = [
            # Import langsung dari pydantic.types
            (r'from pydantic\.types import ([^,\n]*,\s*)?Base64Str([,\s][^,\n]*)?', 
             r'from src.infrastructure.utils.pydantic_types import \1DataUriBase64String\2'),
            
            (r'from pydantic\.types import ([^,\n]*,\s*)?Base64Bytes([,\s][^,\n]*)?', 
             r'from src.infrastructure.utils.pydantic_types import \1DataUriBase64BytesType\2'),
            
            # Import dengan alias
            (r'from pydantic import ([^,\n]*,\s*)?Base64Str([,\s][^,\n]*)?', 
             r'from src.infrastructure.utils.pydantic_types import \1DataUriBase64String as Base64Str\2'),
            
            (r'from pydantic import ([^,\n]*,\s*)?Base64Bytes([,\s][^,\n]*)?', 
             r'from src.infrastructure.utils.pydantic_types import \1DataUriBase64BytesType as Base64Bytes\2'),
            
            # Penggunaan langsung dalam kode
            (r'\bBase64Str\b', r'DataUriBase64String'),
            (r'\bBase64Bytes\b', r'DataUriBase64BytesType'),
        ]
        
        # Terapkan perbaikan
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content)
        
        # Tambahkan import yang diperlukan jika ada perubahan
        if content != original_content:
            # Cek apakah sudah ada import dari utils
            if 'from src.infrastructure.utils.pydantic_types import' not in content:
                # Tambahkan import di bagian atas file
                import_line = "from src.infrastructure.utils.pydantic_types import DataUriBase64String, DataUriBase64BytesType\n"
                
                # Cari tempat yang tepat untuk menambahkan import
                lines = content.split('\n')
                insert_index = 0
                
                # Cari baris import terakhir
                for i, line in enumerate(lines):
                    if line.strip().startswith('import ') or line.strip().startswith('from '):
                        insert_index = i + 1
                
                lines.insert(insert_index, import_line.strip())
                content = '\n'.join(lines)
            
            # Tulis file yang sudah diperbaiki
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error memperbaiki {file_path}: {e}")
        return False


def fix_base64_decode_calls(file_path: Path) -> bool:
    """Perbaiki panggilan base64.b64decode langsung."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern untuk mencari base64.b64decode yang mungkin menerima data URI
        patterns_to_fix = [
            # base64.b64decode(data_uri_variable)
            (r'base64\.b64decode\(([^)]+)\)', 
             r'safe_base64_decode(\1)'),
        ]
        
        # Terapkan perbaikan
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content)
        
        # Tambahkan import yang diperlukan jika ada perubahan
        if content != original_content:
            if 'from src.infrastructure.utils.data_uri_utils import safe_base64_decode' not in content:
                import_line = "from src.infrastructure.utils.data_uri_utils import safe_base64_decode\n"
                
                lines = content.split('\n')
                insert_index = 0
                
                for i, line in enumerate(lines):
                    if line.strip().startswith('import ') or line.strip().startswith('from '):
                        insert_index = i + 1
                
                lines.insert(insert_index, import_line.strip())
                content = '\n'.join(lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error memperbaiki decode calls di {file_path}: {e}")
        return False


def scan_and_fix_project():
    """Scan dan perbaiki seluruh proyek."""
    print("🔧 MEMULAI PERBAIKAN ERROR BASE64 DATA URI")
    print("=" * 60)
    
    project_root = Path.cwd()
    src_dir = project_root / "src"
    
    if not src_dir.exists():
        print("❌ Direktori src tidak ditemukan!")
        return
    
    fixed_files = []
    total_files = 0
    
    # Scan semua file Python
    for py_file in src_dir.rglob("*.py"):
        total_files += 1
        
        # Skip file __pycache__
        if "__pycache__" in str(py_file):
            continue
        
        print(f"🔍 Memeriksa: {py_file.relative_to(project_root)}")
        
        # Perbaiki import Base64
        fixed_imports = fix_base64_imports_in_file(py_file)
        
        # Perbaiki panggilan decode langsung
        fixed_decodes = fix_base64_decode_calls(py_file)
        
        if fixed_imports or fixed_decodes:
            fixed_files.append(py_file)
            print(f"   ✅ DIPERBAIKI!")
        else:
            print(f"   ⚪ Tidak perlu diperbaiki")
    
    print("\n" + "=" * 60)
    print("📊 HASIL PERBAIKAN:")
    print(f"   Total file diperiksa: {total_files}")
    print(f"   File yang diperbaiki: {len(fixed_files)}")
    
    if fixed_files:
        print("\n📝 FILE YANG DIPERBAIKI:")
        for file_path in fixed_files:
            print(f"   ✅ {file_path.relative_to(project_root)}")
    
    print("\n🎉 PERBAIKAN SELESAI!")
    print("Error 'Invalid character (at character 5)' seharusnya sudah teratasi!")


def create_quick_fix_function():
    """Buat fungsi perbaikan cepat yang bisa digunakan langsung."""
    fix_code = '''
def decode_data_uri_quick_fix(data_uri_string):
    """
    FUNGSI PERBAIKAN CEPAT - Copy paste ini ke kode Anda!
    
    Mengganti base64.b64decode() yang error dengan fungsi yang benar.
    """
    import base64
    
    if not isinstance(data_uri_string, str):
        return None
    
    # Cek apakah ini data URI
    if data_uri_string.startswith('data:'):
        # Cari koma yang memisahkan header dari data
        comma_pos = data_uri_string.find(',')
        if comma_pos == -1:
            return None
        
        # Ambil hanya bagian base64 (setelah koma)
        base64_content = data_uri_string[comma_pos + 1:]
    else:
        # Sudah base64 biasa
        base64_content = data_uri_string
    
    # Decode base64
    try:
        return base64.b64decode(base64_content)
    except:
        return None

# CARA PAKAI:
# Ganti ini:     decoded = base64.b64decode(your_data_uri)
# Dengan ini:    decoded = decode_data_uri_quick_fix(your_data_uri)
'''
    
    with open("quick_fix_function.py", "w", encoding="utf-8") as f:
        f.write(fix_code)
    
    print("📄 File 'quick_fix_function.py' dibuat!")
    print("   Copy paste fungsi dari file ini ke kode Anda untuk perbaikan cepat!")


def main():
    """Main function."""
    print("🚨 PERBAIKAN ERROR BASE64 DATA URI 🚨")
    print("Error: 'Invalid character (at character 5)'")
    print()
    
    choice = input("Pilih opsi perbaikan:\n1. Scan dan perbaiki seluruh proyek (otomatis)\n2. Buat fungsi perbaikan cepat\n3. Keduanya\nPilihan (1/2/3): ").strip()
    
    if choice in ["1", "3"]:
        scan_and_fix_project()
    
    if choice in ["2", "3"]:
        print("\n" + "=" * 60)
        create_quick_fix_function()
    
    print("\n" + "🎯" * 20)
    print("ERROR BASE64 DATA URI SUDAH DIPERBAIKI!")
    print("Jalankan aplikasi Anda lagi - error seharusnya hilang!")
    print("🎯" * 20)


if __name__ == "__main__":
    main()
