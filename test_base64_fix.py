#!/usr/bin/env python3
"""Test script to demonstrate the base64 data URI fix.

This script shows how the new utilities and Pydantic types can handle
data URIs properly, fixing the "Invalid character (at character 5)" error.
"""

import base64
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pydantic import BaseModel, ValidationError
from pydantic.types import Base64Str  # Original Pydantic type that fails

from src.infrastructure.utils.data_uri_utils import (
    parse_data_uri,
    extract_base64_from_data_uri,
    safe_base64_decode,
    decode_data_uri_base64
)
from src.infrastructure.utils.pydantic_types import (
    DataUriBase64String,
    DataUriBase64BytesType
)


def test_original_problem():
    """Demonstrate the original problem with Pydantic Base64Str."""
    print("=" * 60)
    print("1. DEMONSTRATING THE ORIGINAL PROBLEM")
    print("=" * 60)
    
    # This is the problematic data URI that causes the error
    problematic_data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAEvklEQ"
    
    print(f"Problematic data URI: {problematic_data_uri[:50]}...")
    print(f"Character 5 is: '{problematic_data_uri[4]}' (the colon in 'data:')")
    
    class OriginalModel(BaseModel):
        image_data: Base64Str
    
    try:
        # This will fail with "Invalid character (at character 5)"
        model = OriginalModel(image_data=problematic_data_uri)
        print("✅ Original Pydantic Base64Str worked (unexpected!)")
    except ValidationError as e:
        print(f"❌ Original Pydantic Base64Str failed as expected:")
        print(f"   Error: {e}")
        print(f"   This is the error you were experiencing!")


def test_utility_functions():
    """Test the new utility functions."""
    print("\n" + "=" * 60)
    print("2. TESTING NEW UTILITY FUNCTIONS")
    print("=" * 60)
    
    # Create a valid test data URI
    test_data = b"Hello, World! This is test image data."
    test_base64 = base64.b64encode(test_data).decode('ascii')
    test_data_uri = f"data:image/png;base64,{test_base64}"
    
    print(f"Test data URI: {test_data_uri}")
    
    # Test parsing
    print("\n--- Testing parse_data_uri ---")
    parsed = parse_data_uri(test_data_uri)
    if parsed:
        media_type, is_base64, data = parsed
        print(f"✅ Parsed successfully:")
        print(f"   Media type: {media_type}")
        print(f"   Is base64: {is_base64}")
        print(f"   Data length: {len(data)}")
    else:
        print("❌ Failed to parse data URI")
    
    # Test base64 extraction
    print("\n--- Testing extract_base64_from_data_uri ---")
    extracted = extract_base64_from_data_uri(test_data_uri)
    if extracted:
        print(f"✅ Extracted base64 content: {extracted[:20]}...")
        print(f"   Length: {len(extracted)}")
    else:
        print("❌ Failed to extract base64 content")
    
    # Test safe decoding
    print("\n--- Testing safe_base64_decode ---")
    decoded = safe_base64_decode(test_data_uri)
    if decoded:
        print(f"✅ Safely decoded data URI:")
        print(f"   Decoded bytes: {decoded}")
        print(f"   As string: {decoded.decode('utf-8')}")
    else:
        print("❌ Failed to safely decode data URI")
    
    # Test with raw base64 (should also work)
    print("\n--- Testing with raw base64 ---")
    raw_decoded = safe_base64_decode(test_base64)
    if raw_decoded:
        print(f"✅ Raw base64 also works: {raw_decoded.decode('utf-8')}")
    else:
        print("❌ Failed to decode raw base64")


def test_new_pydantic_types():
    """Test the new Pydantic types that handle data URIs."""
    print("\n" + "=" * 60)
    print("3. TESTING NEW PYDANTIC TYPES")
    print("=" * 60)
    
    # Create test data
    test_data = b"Hello, World! This is test image data."
    test_base64 = base64.b64encode(test_data).decode('ascii')
    test_data_uri = f"data:image/png;base64,{test_base64}"
    
    class FixedModel(BaseModel):
        image_data_str: DataUriBase64String
        image_data_bytes: DataUriBase64BytesType
    
    print("--- Testing with data URI ---")
    try:
        model = FixedModel(
            image_data_str=test_data_uri,
            image_data_bytes=test_data_uri
        )
        print("✅ New Pydantic types work with data URI!")
        print(f"   String field: {model.image_data_str[:20]}...")
        print(f"   Bytes field: {model.image_data_bytes}")
        print(f"   Bytes as string: {model.image_data_bytes.decode('utf-8')}")
    except ValidationError as e:
        print(f"❌ New Pydantic types failed: {e}")
    
    print("\n--- Testing with raw base64 ---")
    try:
        model2 = FixedModel(
            image_data_str=test_base64,
            image_data_bytes=test_base64
        )
        print("✅ New Pydantic types also work with raw base64!")
        print(f"   String field: {model2.image_data_str[:20]}...")
        print(f"   Bytes field: {model2.image_data_bytes}")
    except ValidationError as e:
        print(f"❌ New Pydantic types failed with raw base64: {e}")
    
    print("\n--- Testing serialization ---")
    try:
        model_dict = model.model_dump()
        print("✅ Serialization works:")
        print(f"   Serialized: {model_dict}")
    except Exception as e:
        print(f"❌ Serialization failed: {e}")


def test_problematic_case():
    """Test with the exact problematic case from the error message."""
    print("\n" + "=" * 60)
    print("4. TESTING THE EXACT PROBLEMATIC CASE")
    print("=" * 60)
    
    # This is the exact data URI from the error message
    problematic_data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAEvklEQ"
    
    print(f"Problematic data URI: {problematic_data_uri}")
    
    # Test with utility functions
    print("\n--- Testing with utility functions ---")
    decoded = safe_base64_decode(problematic_data_uri)
    if decoded:
        print(f"✅ Utility function handled it: {len(decoded)} bytes decoded")
    else:
        print("❌ Utility function failed")
    
    # Test with new Pydantic type
    print("\n--- Testing with new Pydantic type ---")
    class TestModel(BaseModel):
        data: DataUriBase64BytesType
    
    try:
        model = TestModel(data=problematic_data_uri)
        print(f"✅ New Pydantic type handled it: {len(model.data)} bytes")
    except ValidationError as e:
        print(f"❌ New Pydantic type failed: {e}")


def main():
    """Run all tests."""
    print("BASE64 DATA URI FIX DEMONSTRATION")
    print("This script demonstrates the fix for the base64 decoding error")
    print("when handling data URIs with Pydantic validation.")
    
    test_original_problem()
    test_utility_functions()
    test_new_pydantic_types()
    test_problematic_case()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("✅ The new utilities and Pydantic types can handle:")
    print("   - Data URIs (data:image/png;base64,<content>)")
    print("   - Raw base64 strings")
    print("   - Proper validation and error handling")
    print("   - Seamless integration with existing Pydantic models")
    print("\n🔧 TO FIX YOUR CODE:")
    print("   1. Replace 'Base64Str' with 'DataUriBase64String'")
    print("   2. Replace 'Base64Bytes' with 'DataUriBase64BytesType'")
    print("   3. Import from: src.infrastructure.utils.pydantic_types")
    print("\n📝 EXAMPLE:")
    print("   from src.infrastructure.utils.pydantic_types import DataUriBase64String")
    print("   class MyModel(BaseModel):")
    print("       image: DataUriBase64String  # Now handles data URIs!")


if __name__ == "__main__":
    main()
