"""Infrastructure layer for external integrations and adapters.

This package contains adapters for external systems like cameras,
pose detection engines, and other hardware/software integrations.
"""

from .utils.data_uri_utils import (
    parse_data_uri,
    extract_base64_from_data_uri,
    validate_data_uri,
    decode_data_uri_base64,
    safe_base64_decode,
    create_data_uri,
    is_image_data_uri
)

__all__ = [
    'parse_data_uri',
    'extract_base64_from_data_uri',
    'validate_data_uri',
    'decode_data_uri_base64',
    'safe_base64_decode',
    'create_data_uri',
    'is_image_data_uri'
]