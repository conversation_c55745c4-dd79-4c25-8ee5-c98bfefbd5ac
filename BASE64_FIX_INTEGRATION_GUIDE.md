
# 🔧 PANDUAN INTEGRASI PERBAIKAN BASE64 DATA URI

## <PERSON><PERSON><PERSON> yang Diper<PERSON>iki
Error: "Invalid character (at character 5)" saat decode data URI

## Solusi 1: <PERSON><PERSON><PERSON> (Recommended)
```python
def safe_decode_data_uri(data_input):
    """Decode base64 yang bisa menangani data URIs."""
    if isinstance(data_input, bytes):
        data_input = data_input.decode('utf-8', errors='ignore')
    
    if isinstance(data_input, str) and data_input.strip().lower().startswith('data:'):
        comma_pos = data_input.find(',')
        if comma_pos == -1:
            raise ValueError("Invalid data URI: no comma found")
        base64_content = data_input[comma_pos + 1:]
    else:
        base64_content = data_input
    
    return base64.b64decode(base64_content)

# Ganti ini:
# decoded = base64.b64decode(your_data_uri)

# Dengan ini:
decoded = safe_decode_data_uri(your_data_uri)
```

## Solusi 2: Patch Global (Automatic)
```python
# Tambahkan di awal aplikasi Anda:
import base64

original_b64decode = base64.b64decode

def patched_b64decode(s, altchars=None, validate=False):
    if isinstance(s, str) and s.strip().lower().startswith('data:'):
        comma_pos = s.find(',')
        if comma_pos != -1:
            s = s[comma_pos + 1:]
    return original_b64decode(s, altchars=altchars, validate=validate)

base64.b64decode = patched_b64decode
```

## Solusi 3: Import Otomatis
```python
# Tambahkan di awal main file Anda:
import sys
sys.path.insert(0, 'src')
import src.infrastructure  # Ini akan mengaktifkan patch otomatis
```

## Hasil
✅ Error "Invalid character (at character 5)" akan hilang
✅ Data URIs akan di-decode dengan benar
✅ Base64 normal tetap bekerja
✅ Backward compatibility terjaga
