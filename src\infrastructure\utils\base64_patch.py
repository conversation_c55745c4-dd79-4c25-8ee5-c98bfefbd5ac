"""
Global patch untuk base64.b64decode agar bisa menangani data URIs.

Module ini akan mengganti fungsi base64.b64decode secara global
sehingga semua kode yang menggunakan base64.b64decode akan otomatis
bisa menangani data URIs tanpa perlu diubah.
"""

import base64
import logging
from typing import Union, Optional

logger = logging.getLogger(__name__)

# Simpan fungsi asli
_original_b64decode = base64.b64decode


def patched_b64decode(s: Union[str, bytes], altchars=None, validate=False) -> bytes:
    """
    Versi yang dipatch dari base64.b64decode yang bisa menangani data URIs.
    
    Fungsi ini akan otomatis mendeteksi data URIs dan mengekstrak
    konten base64 sebelum melakukan decode.
    
    Args:
        s: String atau bytes untuk di-decode
        altchars: Karakter alternatif (sama seperti base64.b64decode asli)
        validate: Validasi input (sama seperti base64.b64decode asli)
        
    Returns:
        Bytes hasil decode
        
    Raises:
        Exception: Jika decoding gagal
    """
    # Konversi ke string jika input adalah bytes
    if isinstance(s, bytes):
        s = s.decode('utf-8', errors='ignore')
    
    # Cek apakah ini data URI
    if isinstance(s, str) and s.strip().lower().startswith('data:'):
        logger.debug("Detected data URI, extracting base64 content")
        
        # Cari koma yang memisahkan header dari data
        comma_pos = s.find(',')
        if comma_pos == -1:
            raise ValueError("Invalid data URI format: no comma found")
        
        # Ambil hanya bagian base64 (setelah koma)
        base64_content = s[comma_pos + 1:]
        logger.debug(f"Extracted base64 content: {len(base64_content)} characters")
        
        # Gunakan konten base64 untuk decoding
        s = base64_content
    
    # Panggil fungsi base64.b64decode asli
    try:
        result = _original_b64decode(s, altchars=altchars, validate=validate)
        logger.debug(f"Successfully decoded {len(result)} bytes")
        return result
    except Exception as e:
        logger.error(f"Base64 decoding failed: {e}")
        raise


def apply_base64_patch():
    """
    Terapkan patch ke base64.b64decode secara global.
    
    Setelah fungsi ini dipanggil, semua kode yang menggunakan
    base64.b64decode akan otomatis bisa menangani data URIs.
    """
    logger.info("Applying global base64.b64decode patch for data URI support")
    base64.b64decode = patched_b64decode
    logger.info("✅ Base64 patch applied successfully")


def remove_base64_patch():
    """
    Hapus patch dan kembalikan fungsi base64.b64decode asli.
    """
    logger.info("Removing base64.b64decode patch")
    base64.b64decode = _original_b64decode
    logger.info("✅ Base64 patch removed, original function restored")


def is_patch_applied() -> bool:
    """
    Cek apakah patch sudah diterapkan.
    
    Returns:
        True jika patch sudah diterapkan, False jika belum
    """
    return base64.b64decode == patched_b64decode


# Auto-apply patch saat module di-import
if not is_patch_applied():
    apply_base64_patch()


# Fungsi utilitas tambahan
def safe_decode_data_uri(data_uri: str) -> Optional[bytes]:
    """
    Fungsi utilitas untuk decode data URI dengan aman.
    
    Args:
        data_uri: Data URI string
        
    Returns:
        Bytes hasil decode atau None jika gagal
    """
    try:
        return patched_b64decode(data_uri)
    except Exception as e:
        logger.error(f"Failed to decode data URI: {e}")
        return None


def test_patch():
    """
    Test fungsi patch dengan contoh data URI.
    """
    print("🧪 Testing base64 patch...")
    
    # Test data URI
    test_data = b"Hello, World! This is test data."
    test_base64 = base64.b64encode(test_data).decode('ascii')
    test_data_uri = f"data:image/png;base64,{test_base64}"
    
    print(f"Original data: {test_data}")
    print(f"Base64: {test_base64}")
    print(f"Data URI: {test_data_uri}")
    
    # Test dengan patch
    try:
        decoded = base64.b64decode(test_data_uri)
        print(f"✅ Patched decode success: {decoded}")
        print(f"✅ Matches original: {decoded == test_data}")
    except Exception as e:
        print(f"❌ Patched decode failed: {e}")
    
    # Test dengan base64 biasa
    try:
        decoded_normal = base64.b64decode(test_base64)
        print(f"✅ Normal base64 still works: {decoded_normal == test_data}")
    except Exception as e:
        print(f"❌ Normal base64 failed: {e}")


if __name__ == "__main__":
    test_patch()
