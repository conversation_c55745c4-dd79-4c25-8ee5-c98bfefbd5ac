"""
Base64 Data URI Patch - Import file ini untuk memperbaiki error otomatis.

Usage:
    import base64_data_uri_patch  # Error akan otomatis diperbaiki
"""

import base64

# Simpan fungsi asli
_original_b64decode = base64.b64decode

def _patched_b64decode(s, altchars=None, validate=False):
    """Versi yang dipatch dari base64.b64decode untuk menangani data URIs."""
    
    # Konversi ke string jika perlu
    if isinstance(s, bytes):
        s = s.decode('utf-8', errors='ignore')
    
    # Cek apakah ini data URI
    if isinstance(s, str) and s.strip().lower().startswith('data:'):
        comma_pos = s.find(',')
        if comma_pos != -1:
            s = s[comma_pos + 1:]  # Ambil hanya bagian base64
    
    # Panggil fungsi asli
    return _original_b64decode(s, altchars=altchars, validate=validate)

# Terapkan patch
base64.b64decode = _patched_b64decode

print("✅ Base64 Data URI patch applied! Error 'Invalid character (at character 5)' fixed!")
