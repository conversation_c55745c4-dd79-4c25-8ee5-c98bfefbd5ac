
def decode_data_uri_quick_fix(data_uri_string):
    """
    FUNGSI PERBAIKAN CEPAT - Copy paste ini ke kode Anda!
    
    Mengganti base64.b64decode() yang error dengan fungsi yang benar.
    """
    import base64
    
    if not isinstance(data_uri_string, str):
        return None
    
    # Cek apakah ini data URI
    if data_uri_string.startswith('data:'):
        # Cari koma yang memisahkan header dari data
        comma_pos = data_uri_string.find(',')
        if comma_pos == -1:
            return None
        
        # Ambil hanya bagian base64 (setelah koma)
        base64_content = data_uri_string[comma_pos + 1:]
    else:
        # Sudah base64 biasa
        base64_content = data_uri_string
    
    # Decode base64
    try:
        return base64.b64decode(base64_content)
    except:
        return None

# CARA PAKAI:
# Ganti ini:     decoded = base64.b64decode(your_data_uri)
# Dengan ini:    decoded = decode_data_uri_quick_fix(your_data_uri)
