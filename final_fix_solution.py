#!/usr/bin/env python3
"""
SOLUSI FINAL UNTUK ERROR BASE64 DATA URI

Script ini akan memberikan solusi yang paling sederhana dan efektif
untuk memperbaiki error "Invalid character (at character 5)".
"""

import base64
import sys
from pathlib import Path

def safe_decode_data_uri(data_input):
    """
    FUNGSI PERBAIKAN UTAMA
    
    Fungsi ini mengganti base64.b64decode() dan bisa menangani:
    1. Data URIs (data:image/png;base64,...)
    2. Base64 strings biasa
    3. Bytes input
    
    Args:
        data_input: String data URI, base64 string, atau bytes
        
    Returns:
        bytes: Data yang sudah di-decode
        
    Raises:
        ValueError: Jika input tidak valid
    """
    # Konversi bytes ke string jika perlu
    if isinstance(data_input, bytes):
        data_input = data_input.decode('utf-8', errors='ignore')
    
    if not isinstance(data_input, str):
        raise ValueError(f"Expected string or bytes, got {type(data_input)}")
    
    # Cek apakah ini data URI
    if data_input.strip().lower().startswith('data:'):
        # Cari koma yang memisahkan header dari data
        comma_pos = data_input.find(',')
        if comma_pos == -1:
            raise ValueError("Invalid data URI: no comma found")
        
        # Ambil hanya bagian base64 (setelah koma)
        base64_content = data_input[comma_pos + 1:]
        print(f"🔧 Data URI detected, extracted base64: {base64_content[:20]}...")
    else:
        # Sudah base64 biasa
        base64_content = data_input
    
    # Decode base64
    try:
        return base64.b64decode(base64_content)
    except Exception as e:
        raise ValueError(f"Base64 decoding failed: {e}")


def apply_global_fix():
    """
    Terapkan perbaikan secara global dengan mengganti base64.b64decode.
    """
    print("🔧 Applying global base64.b64decode fix...")
    
    # Simpan fungsi asli
    original_b64decode = base64.b64decode
    
    def patched_b64decode(s, altchars=None, validate=False):
        """Versi yang dipatch dari base64.b64decode."""
        try:
            # Coba dengan fungsi safe kita
            return safe_decode_data_uri(s)
        except ValueError:
            # Jika gagal, coba dengan fungsi asli
            return original_b64decode(s, altchars=altchars, validate=validate)
    
    # Ganti fungsi global
    base64.b64decode = patched_b64decode
    print("✅ Global patch applied!")


def test_all_cases():
    """Test semua kasus untuk memastikan fix bekerja."""
    print("\n🧪 TESTING ALL CASES")
    print("=" * 50)
    
    test_cases = [
        # Case 1: Data URI yang menyebabkan error asli
        {
            'name': 'Original Error Case',
            'input': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
            'expected_length': 67  # Length of 1x1 PNG
        },
        
        # Case 2: Data URI dengan data yang valid
        {
            'name': 'Valid Data URI',
            'input': 'data:image/png;base64,SGVsbG8gV29ybGQ=',
            'expected_result': b'Hello World'
        },
        
        # Case 3: Base64 biasa
        {
            'name': 'Normal Base64',
            'input': 'SGVsbG8gV29ybGQ=',
            'expected_result': b'Hello World'
        },
        
        # Case 4: Data URI dengan JPEG
        {
            'name': 'JPEG Data URI',
            'input': 'data:image/jpeg;base64,VGVzdCBKUEVH',
            'expected_result': b'Test JPEG'
        }
    ]
    
    results = []
    
    for case in test_cases:
        print(f"\n--- Testing: {case['name']} ---")
        print(f"Input: {case['input'][:50]}...")
        
        try:
            # Test dengan fungsi safe kita
            result = safe_decode_data_uri(case['input'])
            
            # Test dengan base64.b64decode yang sudah di-patch
            result_patched = base64.b64decode(case['input'])
            
            # Validasi hasil
            if 'expected_result' in case:
                success = result == case['expected_result'] and result_patched == case['expected_result']
                print(f"✅ SUCCESS: {result}")
            elif 'expected_length' in case:
                success = len(result) == case['expected_length'] and len(result_patched) == case['expected_length']
                print(f"✅ SUCCESS: {len(result)} bytes decoded")
            else:
                success = True
                print(f"✅ SUCCESS: {len(result)} bytes decoded")
            
            results.append((case['name'], success))
            
        except Exception as e:
            print(f"❌ FAILED: {e}")
            results.append((case['name'], False))
    
    return results


def create_integration_guide():
    """Buat panduan integrasi untuk pengguna."""
    guide = '''
# 🔧 PANDUAN INTEGRASI PERBAIKAN BASE64 DATA URI

## Masalah yang Diperbaiki
Error: "Invalid character (at character 5)" saat decode data URI

## Solusi 1: Gunakan Fungsi Safe (Recommended)
```python
def safe_decode_data_uri(data_input):
    """Decode base64 yang bisa menangani data URIs."""
    if isinstance(data_input, bytes):
        data_input = data_input.decode('utf-8', errors='ignore')
    
    if isinstance(data_input, str) and data_input.strip().lower().startswith('data:'):
        comma_pos = data_input.find(',')
        if comma_pos == -1:
            raise ValueError("Invalid data URI: no comma found")
        base64_content = data_input[comma_pos + 1:]
    else:
        base64_content = data_input
    
    return base64.b64decode(base64_content)

# Ganti ini:
# decoded = base64.b64decode(your_data_uri)

# Dengan ini:
decoded = safe_decode_data_uri(your_data_uri)
```

## Solusi 2: Patch Global (Automatic)
```python
# Tambahkan di awal aplikasi Anda:
import base64

original_b64decode = base64.b64decode

def patched_b64decode(s, altchars=None, validate=False):
    if isinstance(s, str) and s.strip().lower().startswith('data:'):
        comma_pos = s.find(',')
        if comma_pos != -1:
            s = s[comma_pos + 1:]
    return original_b64decode(s, altchars=altchars, validate=validate)

base64.b64decode = patched_b64decode
```

## Solusi 3: Import Otomatis
```python
# Tambahkan di awal main file Anda:
import sys
sys.path.insert(0, 'src')
import src.infrastructure  # Ini akan mengaktifkan patch otomatis
```

## Hasil
✅ Error "Invalid character (at character 5)" akan hilang
✅ Data URIs akan di-decode dengan benar
✅ Base64 normal tetap bekerja
✅ Backward compatibility terjaga
'''
    
    with open('BASE64_FIX_INTEGRATION_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("📄 Integration guide created: BASE64_FIX_INTEGRATION_GUIDE.md")


def main():
    """Main function."""
    print("🚨 SOLUSI FINAL - BASE64 DATA URI ERROR FIX")
    print("=" * 60)
    print("Memperbaiki error: 'Invalid character (at character 5)'")
    print()
    
    # Apply global fix
    apply_global_fix()
    
    # Test all cases
    results = test_all_cases()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 HASIL TEST")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status}: {name}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    # Create integration guide
    create_integration_guide()
    
    if passed == total:
        print("\n🎉" * 20)
        print("🎉 SEMUA TEST BERHASIL!")
        print("🎉 ERROR BASE64 DATA URI SUDAH DIPERBAIKI!")
        print("🎉 Gunakan panduan integrasi untuk menerapkan fix!")
        print("🎉" * 20)
        
        print("\n📋 LANGKAH SELANJUTNYA:")
        print("1. Copy fungsi safe_decode_data_uri() ke kode Anda")
        print("2. Ganti base64.b64decode() dengan safe_decode_data_uri()")
        print("3. Atau gunakan patch global di awal aplikasi")
        print("4. Test aplikasi Anda - error seharusnya hilang!")
        
    else:
        print("\n⚠️  BEBERAPA TEST GAGAL")
        print("Periksa implementasi dan coba lagi")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
