"""Custom Pydantic types for handling data URIs and base64 content.

This module provides Pydantic-compatible types that can properly handle
data URIs by extracting the base64 content before validation.
"""

import base64
from typing import Any, Union
from typing_extensions import Annotated

from pydantic import GetCoreSchemaHandler, GetJsonSchemaHandler
from pydantic_core import core_schema, PydanticCustomError
from pydantic.json_schema import JsonSchemaValue

from .data_uri_utils import extract_base64_from_data_uri, safe_base64_decode
from src.infrastructure.utils.data_uri_utils import safe_base64_decode


class DataUriBase64Encoder:
    """Custom encoder that handles both data URIs and raw base64 strings."""
    
    @classmethod
    def decode(cls, data: Union[str, bytes]) -> bytes:
        """Decode base64 data, handling both raw base64 and data URIs.
        
        Args:
            data: Either a raw base64 string/bytes or a data URI string
            
        Returns:
            Decoded bytes
            
        Raises:
            PydanticCustomError: If decoding fails
        """
        if isinstance(data, bytes):
            data = data.decode('utf-8', errors='ignore')
        
        if not isinstance(data, str):
            raise PydanticCustomError(
                'data_uri_base64_decode',
                "Expected string or bytes, got {type}",
                {'type': type(data).__name__}
            )
        
        # Try to decode using our safe decoder
        decoded = safe_base64_decode(data)
        if decoded is None:
            raise PydanticCustomError(
                'data_uri_base64_decode',
                "Invalid base64 or data URI format",
                {}
            )
        
        return decoded
    
    @classmethod
    def encode(cls, value: bytes) -> str:
        """Encode bytes to base64 string.
        
        Args:
            value: Bytes to encode
            
        Returns:
            Base64 encoded string
        """
        return base64.b64encode(value).decode('ascii')
    
    @classmethod
    def get_json_format(cls) -> str:
        """Get JSON schema format."""
        return 'base64'


class DataUriBase64Str:
    """A string type that can handle both data URIs and raw base64 strings.
    
    This type automatically detects if the input is a data URI and extracts
    the base64 content before validation and decoding.
    """
    
    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        source_type: Any,
        handler: GetCoreSchemaHandler,
    ) -> core_schema.CoreSchema:
        """Generate the core schema for validation."""
        
        def validate_data_uri_base64(value: Any) -> str:
            """Validate and process data URI or base64 string."""
            if isinstance(value, bytes):
                value = value.decode('utf-8', errors='ignore')
            
            if not isinstance(value, str):
                raise PydanticCustomError(
                    'data_uri_base64_type',
                    "Expected string or bytes, got {type}",
                    {'type': type(value).__name__}
                )
            
            # Check if it's a data URI and extract base64 content
            if value.strip().lower().startswith('data:'):
                base64_content = extract_base64_from_data_uri(value)
                if base64_content is None:
                    raise PydanticCustomError(
                        'data_uri_invalid',
                        "Invalid data URI format or not base64 encoded",
                        {}
                    )
                value = base64_content
            
            # Validate that it's valid base64
            try:
                base64.b64decode(value, validate=True)
            except Exception as e:
                raise PydanticCustomError(
                    'base64_decode',
                    "Base64 decoding error: '{error}'",
                    {'error': str(e)}
                )
            
            return value
        
        return core_schema.no_info_after_validator_function(
            validate_data_uri_base64,
            core_schema.str_schema(),
            serialization=core_schema.to_string_ser_schema(),
        )
    
    @classmethod
    def __get_pydantic_json_schema__(
        cls, core_schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        """Generate JSON schema."""
        json_schema = handler(core_schema)
        json_schema.update({
            'type': 'string',
            'format': 'base64',
            'description': 'Base64 encoded string or data URI'
        })
        return json_schema


class DataUriBase64Bytes:
    """A bytes type that can handle both data URIs and raw base64 strings.
    
    This type automatically detects if the input is a data URI, extracts
    the base64 content, and returns the decoded bytes.
    """
    
    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        source_type: Any,
        handler: GetCoreSchemaHandler,
    ) -> core_schema.CoreSchema:
        """Generate the core schema for validation."""
        
        def validate_and_decode_data_uri_base64(value: Any) -> bytes:
            """Validate and decode data URI or base64 string to bytes."""
            if isinstance(value, bytes):
                # If already bytes, assume it's the decoded content
                return value
            
            if not isinstance(value, str):
                raise PydanticCustomError(
                    'data_uri_base64_type',
                    "Expected string or bytes, got {type}",
                    {'type': type(value).__name__}
                )
            
            # Use our safe decoder that handles both data URIs and raw base64
            decoded = safe_base64_decode(value)
            if decoded is None:
                raise PydanticCustomError(
                    'data_uri_base64_decode',
                    "Invalid base64 or data URI format",
                    {}
                )
            
            return decoded
        
        return core_schema.no_info_after_validator_function(
            validate_and_decode_data_uri_base64,
            core_schema.union_schema([
                core_schema.str_schema(),
                core_schema.bytes_schema(),
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda value: base64.b64encode(value).decode('ascii'),
                return_schema=core_schema.str_schema(),
            ),
        )
    
    @classmethod
    def __get_pydantic_json_schema__(
        cls, core_schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        """Generate JSON schema."""
        json_schema = handler(core_schema)
        json_schema.update({
            'type': 'string',
            'format': 'base64',
            'description': 'Base64 encoded string or data URI that decodes to bytes'
        })
        return json_schema


# Type aliases for easier use
DataUriBase64String = Annotated[str, DataUriBase64Str]
DataUriBase64BytesType = Annotated[bytes, DataUriBase64Bytes]

# Export the types
__all__ = [
    'DataUriBase64Encoder',
    'DataUriBase64Str',
    'DataUriBase64Bytes',
    'DataUriBase64String',
    'DataUriBase64BytesType'
]
