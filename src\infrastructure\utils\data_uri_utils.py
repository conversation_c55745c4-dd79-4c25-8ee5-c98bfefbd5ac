"""Data URI utilities for handling base64-encoded data URIs.

This module provides utilities to properly parse and extract base64 content
from data URIs, fixing the common issue where base64 decoders receive the
full data URI string instead of just the base64 content.
"""

import base64
import re
from typing import Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)

# Data URI pattern: data:[<mediatype>][;base64],<data>
DATA_URI_PATTERN = re.compile(r'^data:([^;,]+)?(;base64)?,(.*)$', re.IGNORECASE)


def parse_data_uri(data_uri: str) -> Optional[Tuple[str, bool, str]]:
    """Parse a data URI into its components.
    
    Args:
        data_uri: The data URI string to parse
        
    Returns:
        Tuple of (media_type, is_base64, data) if valid, None if invalid
        
    Example:
        >>> parse_data_uri("data:image/png;base64,iVBORw0KGgoAAAA...")
        ('image/png', True, 'iVBORw0KGgoAAAA...')
    """
    if not isinstance(data_uri, str):
        logger.warning(f"Expected string, got {type(data_uri)}")
        return None
        
    match = DATA_URI_PATTERN.match(data_uri.strip())
    if not match:
        logger.debug(f"Invalid data URI format: {data_uri[:50]}...")
        return None
    
    media_type = match.group(1) or 'text/plain'
    is_base64 = match.group(2) is not None
    data = match.group(3)
    
    return media_type, is_base64, data


def extract_base64_from_data_uri(data_uri: str) -> Optional[str]:
    """Extract base64 content from a data URI.
    
    This function properly handles data URIs by extracting only the base64
    content after the comma, which can then be safely decoded.
    
    Args:
        data_uri: The data URI string (e.g., "data:image/png;base64,iVBORw0...")
        
    Returns:
        The base64 content string if valid, None if invalid or not base64
        
    Example:
        >>> extract_base64_from_data_uri("data:image/png;base64,iVBORw0KGgoAAAA...")
        'iVBORw0KGgoAAAA...'
    """
    parsed = parse_data_uri(data_uri)
    if not parsed:
        return None
        
    media_type, is_base64, data = parsed
    
    if not is_base64:
        logger.warning(f"Data URI is not base64 encoded: {media_type}")
        return None
    
    return data


def validate_data_uri(data_uri: str) -> bool:
    """Validate if a string is a properly formatted data URI.
    
    Args:
        data_uri: The string to validate
        
    Returns:
        True if valid data URI, False otherwise
    """
    return parse_data_uri(data_uri) is not None


def decode_data_uri_base64(data_uri: str) -> Optional[bytes]:
    """Safely decode base64 content from a data URI.
    
    This function handles the complete process of extracting and decoding
    base64 content from a data URI, with proper error handling.
    
    Args:
        data_uri: The data URI string to decode
        
    Returns:
        Decoded bytes if successful, None if failed
        
    Raises:
        ValueError: If the base64 content is invalid
    """
    base64_content = extract_base64_from_data_uri(data_uri)
    if not base64_content:
        logger.error(f"Failed to extract base64 content from data URI")
        return None
    
    try:
        # Decode the base64 content
        decoded_bytes = base64.b64decode(base64_content, validate=True)
        logger.debug(f"Successfully decoded {len(decoded_bytes)} bytes from data URI")
        return decoded_bytes
        
    except Exception as e:
        logger.error(f"Base64 decoding failed: {e}")
        raise ValueError(f"Invalid base64 content in data URI: {e}")


def safe_base64_decode(data: Union[str, bytes]) -> Optional[bytes]:
    """Safely decode base64 data, handling both raw base64 and data URIs.
    
    This function automatically detects if the input is a data URI and
    extracts the base64 content before decoding.
    
    Args:
        data: Either a raw base64 string/bytes or a data URI string
        
    Returns:
        Decoded bytes if successful, None if failed
    """
    if isinstance(data, bytes):
        data = data.decode('utf-8', errors='ignore')
    
    if not isinstance(data, str):
        logger.error(f"Expected string or bytes, got {type(data)}")
        return None
    
    # Check if it's a data URI
    if data.strip().lower().startswith('data:'):
        try:
            return decode_data_uri_base64(data)
        except ValueError as e:
            logger.error(f"Data URI decoding failed: {e}")
            return None
    
    # Try to decode as raw base64
    try:
        decoded_bytes = base64.b64decode(data, validate=True)
        logger.debug(f"Successfully decoded {len(decoded_bytes)} bytes from raw base64")
        return decoded_bytes
        
    except Exception as e:
        logger.error(f"Raw base64 decoding failed: {e}")
        return None


def create_data_uri(data: bytes, media_type: str = 'application/octet-stream') -> str:
    """Create a data URI from binary data.
    
    Args:
        data: Binary data to encode
        media_type: MIME type of the data
        
    Returns:
        Data URI string
    """
    base64_data = base64.b64encode(data).decode('ascii')
    return f"data:{media_type};base64,{base64_data}"


def is_image_data_uri(data_uri: str) -> bool:
    """Check if a data URI contains image data.
    
    Args:
        data_uri: The data URI to check
        
    Returns:
        True if it's an image data URI, False otherwise
    """
    parsed = parse_data_uri(data_uri)
    if not parsed:
        return False
        
    media_type, _, _ = parsed
    return media_type.startswith('image/')


# Example usage and testing
if __name__ == "__main__":
    # Test with a sample data URI
    sample_data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    print("Testing data URI utilities:")
    print(f"Valid data URI: {validate_data_uri(sample_data_uri)}")
    print(f"Is image: {is_image_data_uri(sample_data_uri)}")
    
    parsed = parse_data_uri(sample_data_uri)
    if parsed:
        media_type, is_base64, data = parsed
        print(f"Media type: {media_type}")
        print(f"Is base64: {is_base64}")
        print(f"Data length: {len(data)}")
    
    # Test base64 extraction
    base64_content = extract_base64_from_data_uri(sample_data_uri)
    if base64_content:
        print(f"Extracted base64 length: {len(base64_content)}")
    
    # Test safe decoding
    decoded = safe_base64_decode(sample_data_uri)
    if decoded:
        print(f"Decoded bytes length: {len(decoded)}")
