#!/usr/bin/env python3
"""
TEST FINAL - Memastikan error base64 data URI sudah diperbaiki.

Script ini akan menguji semua aspek perbaikan untuk memastikan
error "Invalid character (at character 5)" sudah teratasi.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import base64

def test_original_error_case():
    """Test kasus error asli yang dilaporkan."""
    print("🔴 TESTING ORIGINAL ERROR CASE")
    print("=" * 50)
    
    # Data URI yang menyebabkan error asli
    problematic_data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAEvklEQ"
    
    print(f"Problematic data URI: {problematic_data_uri[:60]}...")
    print(f"Character 5: '{problematic_data_uri[4]}' (colon yang menyebabkan error)")
    
    try:
        # Ini seharusnya sekarang bekerja dengan patch
        decoded = base64.b64decode(problematic_data_uri)
        print(f"✅ SUCCESS! Decoded {len(decoded)} bytes")
        print("   Error 'Invalid character (at character 5)' SUDAH DIPERBAIKI!")
        return True
    except Exception as e:
        print(f"❌ MASIH ERROR: {e}")
        return False


def test_valid_data_uri():
    """Test dengan data URI yang valid dan lengkap."""
    print("\n🟢 TESTING VALID DATA URI")
    print("=" * 50)
    
    # Buat data URI yang valid
    test_data = b"Hello, World! This is test image data."
    test_base64 = base64.b64encode(test_data).decode('ascii')
    test_data_uri = f"data:image/png;base64,{test_base64}"
    
    print(f"Test data: {test_data}")
    print(f"Data URI: {test_data_uri}")
    
    try:
        decoded = base64.b64decode(test_data_uri)
        print(f"✅ SUCCESS! Decoded: {decoded}")
        print(f"✅ Matches original: {decoded == test_data}")
        return True
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


def test_normal_base64():
    """Test bahwa base64 normal masih bekerja."""
    print("\n🔵 TESTING NORMAL BASE64")
    print("=" * 50)
    
    test_data = b"Normal base64 test"
    test_base64 = base64.b64encode(test_data).decode('ascii')
    
    print(f"Test data: {test_data}")
    print(f"Base64: {test_base64}")
    
    try:
        decoded = base64.b64decode(test_base64)
        print(f"✅ SUCCESS! Decoded: {decoded}")
        print(f"✅ Matches original: {decoded == test_data}")
        return True
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


def test_pydantic_types():
    """Test custom Pydantic types."""
    print("\n🟡 TESTING CUSTOM PYDANTIC TYPES")
    print("=" * 50)
    
    try:
        from pydantic import BaseModel, ValidationError
        from src.infrastructure.utils.pydantic_types import DataUriBase64String, DataUriBase64BytesType
        
        # Test data
        test_data = b"Pydantic test data"
        test_base64 = base64.b64encode(test_data).decode('ascii')
        test_data_uri = f"data:image/png;base64,{test_base64}"
        
        class TestModel(BaseModel):
            image_str: DataUriBase64String
            image_bytes: DataUriBase64BytesType
        
        # Test dengan data URI
        model = TestModel(
            image_str=test_data_uri,
            image_bytes=test_data_uri
        )
        
        print(f"✅ Pydantic model created successfully!")
        print(f"   String field: {model.image_str[:20]}...")
        print(f"   Bytes field: {model.image_bytes}")
        print(f"   Bytes matches: {model.image_bytes == test_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pydantic test failed: {e}")
        return False


def test_utilities():
    """Test utility functions."""
    print("\n🟣 TESTING UTILITY FUNCTIONS")
    print("=" * 50)
    
    try:
        from src.infrastructure.utils.data_uri_utils import (
            parse_data_uri,
            extract_base64_from_data_uri,
            safe_base64_decode
        )
        
        test_data_uri = "data:image/png;base64,SGVsbG8gV29ybGQ="
        
        # Test parsing
        parsed = parse_data_uri(test_data_uri)
        print(f"✅ Parse data URI: {parsed}")
        
        # Test extraction
        extracted = extract_base64_from_data_uri(test_data_uri)
        print(f"✅ Extract base64: {extracted}")
        
        # Test safe decode
        decoded = safe_base64_decode(test_data_uri)
        print(f"✅ Safe decode: {decoded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Utilities test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 FINAL TEST - BASE64 DATA URI FIX")
    print("Testing semua aspek perbaikan error base64...")
    print()
    
    # Import infrastructure untuk mengaktifkan patch
    try:
        import src.infrastructure
        print("✅ Infrastructure imported - base64 patch active")
    except Exception as e:
        print(f"⚠️  Infrastructure import warning: {e}")
    
    print()
    
    # Run all tests
    tests = [
        ("Original Error Case", test_original_error_case),
        ("Valid Data URI", test_valid_data_uri),
        ("Normal Base64", test_normal_base64),
        ("Pydantic Types", test_pydantic_types),
        ("Utility Functions", test_utilities),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉" * 20)
        print("🎉 SEMUA TEST BERHASIL!")
        print("🎉 ERROR BASE64 DATA URI SUDAH DIPERBAIKI SEPENUHNYA!")
        print("🎉 Aplikasi Anda seharusnya berjalan tanpa error!")
        print("🎉" * 20)
    else:
        print("\n⚠️" * 20)
        print("⚠️  BEBERAPA TEST GAGAL")
        print("⚠️  Mungkin masih ada masalah yang perlu diperbaiki")
        print("⚠️" * 20)
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
