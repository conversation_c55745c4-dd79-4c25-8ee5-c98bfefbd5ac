"""Main entry point for the ergonomic assessment application.

This module provides the Flet application bootstrap and serves as the
primary entry point for the cross-platform GUI application.
"""

import flet as ft
import logging
import sys
import threading
import time
import base64
import traceback
from collections import deque
from typing import Optional
import numpy as np
import cv2

from ..core.event_bus import EventBus, EventHandler
from ..core.events import ScoresUpdatedEvent, CameraErrorEvent, PerformanceStatsEvent
from ..infrastructure.camera.windows_camera_adapter import WindowsCameraAdapter
from ..infrastructure.pose.movenet_engine import MoveNetEngine
from ..infrastructure.pose.mediapipe_engine import MediaPipeEngine
from ..domain.ergonomics.services.simple_reba_rula_calculator import SimpleREBARULACalculator
from ..application.use_cases.capture_and_score import CaptureAndScoreUseCase
from ..application.use_cases.generate_recommendation import GenerateRecommendationUseCase
from ..application.use_cases.session_management import SessionManagementUseCase, SessionConfig
from ..application.use_cases.report_generation import ReportGenerationUseCase
from ..domain.ergonomics.value_objects.score import ErgonomicScore
from .gpu.video_surface import VideoSurface
from .gpu.skeleton_renderer import SkeletonRenderer
from .gpu.heatmap_layer import HeatmapLayer
from .ui.widgets.recommendations_panel import RecommendationsPanel
from .ui.widgets.reporting_panel import ReportingPanel


# Configure logging with DEBUG level to capture detailed rendering logs
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ScoreDisplayHandler(EventHandler):
    """Event handler for updating score displays and performance stats in UI."""
    
    def __init__(self, app: 'ErgonomicsApp'):
        self.app = app
    
    def handle(self, event) -> None:
        """Handle score update events synchronously."""
        if isinstance(event, ScoresUpdatedEvent):
            self.app._update_scores_display(event.scores, event.posture_quality)
            # Store current risk level and keypoints for GPU rendering
            self.app.current_risk_level = event.scores.get_reba_risk_level()
            # Extract keypoints directly from event
            if hasattr(event, 'keypoints') and event.keypoints:
                self.app.current_keypoints = event.keypoints
            elif hasattr(event, 'metadata') and event.metadata and 'keypoints' in event.metadata:
                self.app.current_keypoints = event.metadata['keypoints']
            
            # Store frame for optimized video display
            if hasattr(event, 'frame') and event.frame is not None:
                self.app.current_frame = event.frame
            elif hasattr(event, 'metadata') and event.metadata and 'frame' in event.metadata:
                self.app.current_frame = event.metadata['frame']
                
            # Immediately render skeleton overlay if we have keypoints and valid risk level
            if (self.app.current_keypoints and
                self.app.skeleton_renderer and
                self.app.current_risk_level is not None):
                logger.debug(f"Rendering skeleton overlay with {len(self.app.current_keypoints)} keypoints, risk: {self.app.current_risk_level}")
                try:
                    # Try transparent rendering first for best overlay results
                    if hasattr(self.app.skeleton_renderer, 'draw_transparent'):
                        success = self.app.skeleton_renderer.draw_transparent(
                            self.app.current_keypoints,
                            self.app.current_risk_level
                        )
                        if not success:
                            # Fallback to standard rendering
                            self.app.skeleton_renderer.draw(
                                self.app.current_keypoints,
                                self.app.current_risk_level
                            )
                    else:
                        # Standard rendering if transparent method not available
                        self.app.skeleton_renderer.draw(
                            self.app.current_keypoints,
                            self.app.current_risk_level
                        )
                    logger.debug("Skeleton overlay rendered successfully")
                except Exception as e:
                    logger.error(f"Failed to render skeleton overlay: {e}")
                
        elif isinstance(event, CameraErrorEvent):
            self.app._handle_camera_error(event.error_message)
        elif isinstance(event, PerformanceStatsEvent):
            self.app._update_performance_display(event)
    
    async def handle_async(self, event) -> None:
        """Handle score update events asynchronously."""
        self.handle(event)


class ErgonomicsApp:
    """Main application class for the ergonomic assessment system.
    
    Implements real-time webcam pose detection with ergonomic scoring
    and live UI updates for Windows Proof-of-Concept.
    """
    
    def __init__(self):
        """Initialize the ergonomics application."""
        self.page: Optional[ft.Page] = None
        
        # Core components
        self.event_bus = EventBus()
        self.camera_adapter = WindowsCameraAdapter()
        self.pose_engine = None  # Will be initialized based on availability
        self.score_calculator = SimpleREBARULACalculator()
        
        # Use cases
        self.capture_use_case = None
        self.recommendation_use_case = None
        self.session_manager = None
        self.report_generator = None
        
        # UI components
        self.video_surface = None
        self.skeleton_renderer = None
        self.heatmap_layer = None
        self.recommendations_panel = None
        self.reporting_panel = None
        self.gpu_checkbox = None
        self.heatmap_checkbox = None
        self.reba_score_text = None
        self.rula_score_text = None
        self.quality_text = None
        self.status_text = None
        self.start_button = None
        self.stop_button = None
        
        # Session management UI
        self.session_name_field = None
        self.session_status_text = None
        
        # Performance display - separate FPS tracking
        self.capture_fps_text = None
        self.infer_fps_text = None
        self.render_fps_text = None
        self.latency_text = None
        self.performance_status_text = None
        
        # State and adaptive rendering
        self.is_running = False
        self.gpu_enabled = True  # Default to GPU enabled
        self.current_frame = None
        self.current_keypoints = {}
        self.current_risk_level = None
        
        # Adaptive render loop components
        self.frame_queue = deque(maxlen=1)  # Drop stale frames
        self.render_fps_target = 25.0
        self.last_render_time = 0

        # UI update throttling for video preview
        self.last_ui_update = 0
        self.ui_update_interval = 0.033  # ~30 FPS for UI updates
        
    def main(self, page: ft.Page) -> None:
        """Main application entry point called by Flet.
        
        Args:
            page: The main Flet page object
        """
        self.page = page
        self._setup_page_properties()
        self._initialize_components()
        self._build_ui()
        
        # Start video preview if camera is available
        self._start_video_preview()
        
        logger.info("Ergonomic assessment application started successfully")
    
    def _setup_page_properties(self) -> None:
        """Configure basic page properties and settings with professional Indonesian theme."""
        if not self.page:
            return
            
        self.page.title = "Penilaian Ergonomi Real-Time - Windows PoC"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        
        # Create comprehensive professional color scheme
        self.page.theme = ft.Theme(
            color_scheme=ft.ColorScheme(
                primary=ft.Colors.BLUE_800,
                primary_container=ft.Colors.BLUE_100,
                secondary=ft.Colors.TEAL_600,
                secondary_container=ft.Colors.TEAL_50,
                surface=ft.Colors.WHITE,
                surface_variant=ft.Colors.GREY_50,
                background=ft.Colors.GREY_50,
                error=ft.Colors.RED_600,
                error_container=ft.Colors.RED_100,
                on_primary=ft.Colors.WHITE,
                on_secondary=ft.Colors.WHITE,
                on_surface=ft.Colors.BLACK87,
                on_background=ft.Colors.BLACK87,
                outline=ft.Colors.GREY_300,
                shadow=ft.Colors.BLACK26,
            )
        )
        
        self.page.window_width = 1600
        self.page.window_height = 900
        self.page.window_min_width = 1400
        self.page.window_min_height = 800
        self.page.padding = 12
        self.page.scroll = ft.ScrollMode.AUTO
        self.page.bgcolor = ft.Colors.GREY_50
    
    def _initialize_components(self) -> None:
        """Initialize core application components."""
        # Initialize camera adapter first for video display
        try:
            if self.camera_adapter.initialize():
                logger.info("Camera adapter initialized successfully")
            else:
                logger.warning("Camera adapter initialization failed - video display may not work")
        except Exception as e:
            logger.error(f"Camera adapter initialization error: {e}")
        
        # Try to initialize MoveNet first, fallback to MediaPipe
        try:
            self.pose_engine = MoveNetEngine()
            if not self.pose_engine.initialize():
                logger.warning("MoveNet initialization failed, trying MediaPipe")
                self.pose_engine = MediaPipeEngine()
                if not self.pose_engine.initialize():
                    logger.error("Both pose engines failed to initialize")
                    self.pose_engine = None
        except Exception as e:
            logger.error(f"Pose engine initialization error: {e}")
            try:
                self.pose_engine = MediaPipeEngine()
                if not self.pose_engine.initialize():
                    self.pose_engine = None
            except Exception:
                self.pose_engine = None
        
        # Initialize session management
        session_config = SessionConfig(
            auto_save_interval=30.0,
            max_session_duration=480.0,
            enable_real_time_analysis=True,
            recommendation_threshold=4.0
        )
        
        self.session_manager = SessionManagementUseCase(
            event_bus=self.event_bus,
            config=session_config,
            session_data_callback=self._on_session_data_updated
        )
        
        # Initialize report generator
        self.report_generator = ReportGenerationUseCase()
        
        # Initialize recommendation use case
        self.recommendation_use_case = GenerateRecommendationUseCase(
            event_bus=self.event_bus
        )
        
        # Set up event handling
        score_handler = ScoreDisplayHandler(self)
        self.event_bus.subscribe(ScoresUpdatedEvent, score_handler)
        self.event_bus.subscribe(CameraErrorEvent, score_handler)
        self.event_bus.subscribe(PerformanceStatsEvent, score_handler)
        
        # Initialize capture use case if pose engine available
        if self.pose_engine:
            self.capture_use_case = CaptureAndScoreUseCase(
                camera_adapter=self.camera_adapter,
                pose_engine=self.pose_engine,
                score_calculator=self.score_calculator,
                event_bus=self.event_bus,
                target_fps=25.0,  # Increased for ≥25 FPS target
                buffer_size=10
            )
        
        # Initialize UI panels
        self.recommendations_panel = RecommendationsPanel(
            event_bus=self.event_bus,
            max_recommendations=10
        )
        
        self.reporting_panel = ReportingPanel(
            session_manager=self.session_manager,
            report_generator=self.report_generator
        )
    
    def _build_ui(self) -> None:
        """Build the real-time assessment user interface."""
        if not self.page:
            return
        
        # Video display area with GPU acceleration
        self.video_surface = VideoSurface(width=640, height=480)
        overlay_control = self.video_surface.get_overlay_control()
        
        # Initialize GPU renderers
        if overlay_control:
            self.skeleton_renderer = SkeletonRenderer(overlay_control, width=640, height=480)
            self.heatmap_layer = HeatmapLayer(overlay_control, width=640, height=480)
        
        video_container = ft.Container(
            content=self.video_surface.get_control(),
            width=640,
            height=480,
            border=ft.border.all(2, ft.Colors.GREY),
            border_radius=10,
        )
        
        # Score display panel
        self.reba_score_text = ft.Text("REBA: --", size=20, weight=ft.FontWeight.BOLD)
        self.rula_score_text = ft.Text("RULA: --", size=20, weight=ft.FontWeight.BOLD)
        self.quality_text = ft.Text("Kualitas Pose: --", size=16)
        
        # Performance display elements - separate FPS tracking
        self.capture_fps_text = ft.Text("FPS Capture: --", size=14)
        self.infer_fps_text = ft.Text("FPS Inferensi: --", size=14)
        self.render_fps_text = ft.Text("FPS Render: --", size=14, weight=ft.FontWeight.BOLD)
        self.latency_text = ft.Text("Latensi: --", size=14)
        self.performance_status_text = ft.Text("Performa: --", size=14)
        
        scores_panel = ft.Container(
            content=ft.Column([
                ft.Text("Skor Ergonomik", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                self.reba_score_text,
                self.rula_score_text,
                ft.Divider(height=1),
                self.quality_text,
            ], spacing=8, tight=True),
            padding=15,
            border=ft.border.all(1, ft.Colors.BLUE),
            border_radius=8,
            expand=True,
        )
        
        # Performance monitoring panel with separate FPS metrics
        performance_panel = ft.Container(
            content=ft.Column([
                ft.Text("Monitor Performa", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                self.capture_fps_text,
                self.infer_fps_text,
                self.render_fps_text,
                self.latency_text,
                self.performance_status_text,
            ], spacing=6, tight=True),
            padding=15,
            border=ft.border.all(1, ft.Colors.GREEN),
            border_radius=8,
            expand=True,
        )
        
        # Control panel
        self.start_button = ft.ElevatedButton(
            "Mulai Asesmen",
            icon=ft.Icons.PLAY_ARROW,
            on_click=self._on_start_clicked,
            disabled=self.pose_engine is None,
            bgcolor=ft.Colors.GREEN,
            width=140,
            height=36,
        )
        
        self.stop_button = ft.ElevatedButton(
            "Hentikan Asesmen",
            icon=ft.Icons.STOP,
            on_click=self._on_stop_clicked,
            disabled=True,
            bgcolor=ft.Colors.RED,
            width=140,
            height=36,
        )
        
        self.status_text = ft.Text(
            "Siap" if self.pose_engine else "Engine pose tidak tersedia",
            size=14,
            color=ft.Colors.GREEN if self.pose_engine else ft.Colors.RED,
        )
        
        # GPU and Heatmap toggle checkboxes
        self.gpu_checkbox = ft.Checkbox(
            label="Gunakan GPU",
            value=True,  # Default enabled
            on_change=self._on_gpu_toggle,
        )
        
        self.heatmap_checkbox = ft.Checkbox(
            label="Tampilkan Heat-map",
            value=False,
            on_change=self._on_heatmap_toggle,
        )
        
        # Session management controls
        self.session_name_field = ft.TextField(
            label="Nama Sesi",
            value=f"Sesi {time.strftime('%H:%M')}",
            expand=True,
            height=40
        )
        
        self.session_status_text = ft.Text("Tidak ada sesi aktif", size=12, color=ft.Colors.GREY_600)
        
        start_session_button = ft.ElevatedButton(
            "Mulai Sesi",
            icon=ft.Icons.PLAY_CIRCLE,
            on_click=self._on_start_session_clicked,
            bgcolor=ft.Colors.GREEN_200,
            width=140,
            height=36,
        )
        
        stop_session_button = ft.ElevatedButton(
            "Hentikan Sesi",
            icon=ft.Icons.STOP_CIRCLE,
            on_click=self._on_stop_session_clicked,
            bgcolor=ft.Colors.RED_200,
            width=140,
            height=36,
        )
        
        session_controls = ft.Container(
            content=ft.Column([
                ft.Text("Manajemen Sesi", size=14, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                self.session_name_field,
                ft.Row([start_session_button, stop_session_button], spacing=8, wrap=True),
                self.session_status_text,
            ], spacing=8, tight=True),
            padding=12,
            border=ft.border.all(1, ft.Colors.TEAL),
            border_radius=8,
            expand=True,
        )
        
        controls_panel = ft.Container(
            content=ft.Column([
                ft.Text("Kontrol Asesmen", size=14, weight=ft.FontWeight.BOLD),
                ft.Row([self.start_button, self.stop_button], spacing=8, wrap=True),
                self.gpu_checkbox,
                self.heatmap_checkbox,
                self.status_text,
            ], spacing=8, tight=True),
            padding=12,
            border=ft.border.all(1, ft.Colors.GREY),
            border_radius=8,
            expand=True,
        )
        
        # Left column - video and controls
        left_column = ft.Column([
            video_container,
            ft.Row([
                ft.Container(controls_panel, expand=True),
                ft.Container(session_controls, expand=True)
            ], spacing=15),
        ], spacing=15)
        
        # Middle column - scores and performance
        middle_column = ft.Column([
            scores_panel,
            performance_panel,
        ], spacing=12, tight=True, expand=True)
        
        # Right column - recommendations and reporting (in tabs)
        right_panels = ft.Tabs(
            tabs=[
                ft.Tab(
                    text="Rekomendasi",
                    icon=ft.Icons.LIGHTBULB,
                    content=ft.Container(
                        content=self.recommendations_panel.get_control(),
                        padding=8,
                        expand=True,
                        height=650
                    )
                ),
                ft.Tab(
                    text="Laporan",
                    icon=ft.Icons.ANALYTICS,
                    content=ft.Container(
                        content=self.reporting_panel.get_control(),
                        padding=8,
                        expand=True,
                        height=650
                    )
                )
            ],
            selected_index=0,
            expand=True,
            height=700,
            scrollable=True
        )
        
        # Main layout with responsive three columns
        main_row = ft.Row([
            ft.Container(
                content=left_column,
                width=660,
                padding=ft.padding.all(8)
            ),
            ft.Container(
                content=middle_column,
                width=320,
                padding=ft.padding.all(8)
            ),
            ft.Container(
                content=right_panels,
                expand=True,
                padding=ft.padding.all(8)
            ),
        ], spacing=12, alignment=ft.MainAxisAlignment.START, expand=True)
        
        # Title
        title = ft.Text(
            "Penilaian Ergonomik Real-Time",
            size=28,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE,
        )
        
        # Main container
        main_container = ft.Container(
            content=ft.Column([
                title,
                ft.Divider(height=1),
                main_row,
            ], horizontal_alignment=ft.CrossAxisAlignment.START, spacing=15, expand=True),
            padding=12,
            expand=True,
        )
        
        self.page.add(main_container)
        self.page.update()
    
    def _on_start_clicked(self, e) -> None:
        """Handle start assessment button click."""
        if not self.capture_use_case:
            logger.error("Capture use case not initialized")
            self.status_text.value = "Use case capture tidak terinisialisasi"
            self.status_text.color = ft.Colors.RED
            self.page.update()
            return
        
        # Check if session is active
        if not self.session_manager.get_current_session():
            self.status_text.value = "Silakan mulai sesi terlebih dahulu"
            self.status_text.color = ft.Colors.RED
            self.page.update()
            return
        
        # Ensure camera is available before starting
        if not self.camera_adapter.is_opened():
            self.status_text.value = "Menginisialisasi kamera..."
            self.status_text.color = ft.Colors.ORANGE
            self.page.update()
            
            # Give a moment for UI to update
            time.sleep(0.1)
            
            if not self.camera_adapter.initialize():
                self.status_text.value = "Gagal menginisialisasi kamera"
                self.status_text.color = ft.Colors.RED
                self.page.update()
                return
            else:
                logger.info("Camera re-initialized successfully")
        
        # Start recommendation use case
        if self.recommendation_use_case:
            self.recommendation_use_case.start()
        
        if self.capture_use_case.start():
            self.is_running = True
            self.start_button.disabled = True
            self.stop_button.disabled = False
            self.status_text.value = "Asesmen berjalan..."
            self.status_text.color = ft.Colors.BLUE
            self.page.update()
            
            # Start video display update thread
            threading.Thread(target=self._video_update_loop, daemon=True).start()
            
            logger.info("Real-time assessment started")
        else:
            self.status_text.value = "Gagal memulai asesmen"
            self.status_text.color = ft.Colors.RED
            self.page.update()
    
    def _on_stop_clicked(self, e) -> None:
        """Handle stop assessment button click."""
        if self.capture_use_case:
            self.capture_use_case.stop()
        
        # Stop recommendation use case
        if self.recommendation_use_case:
            self.recommendation_use_case.stop()
        
        self.is_running = False
        self.start_button.disabled = False
        self.stop_button.disabled = True
        self.status_text.value = "Asesmen dihentikan"
        self.status_text.color = ft.Colors.GREY
        # Safe UI update with error handling
        try:
            if self.page and hasattr(self.page, 'update'):
                self.page.update()
        except Exception as e:
            logger.warning(f"UI update error during stop: {e}")
        
        logger.info("Real-time assessment stopped")
    
    def _on_gpu_toggle(self, e) -> None:
        """Handle GPU checkbox toggle."""
        try:
            self.gpu_enabled = e.control.value
            logger.info(f"GPU rendering {'enabled' if self.gpu_enabled else 'disabled'}")
            
            # Update page to reflect changes
            if self.page:
                self.page.update()
                
        except Exception as ex:
            logger.error(f"Failed to toggle GPU: {ex}")

    def _video_update_loop(self) -> None:
        """Optimized video display update loop using frames from inference pipeline."""
        while self.is_running:
            try:
                render_start = time.time()
                
                # Throttle render FPS to prevent UI overload
                time_since_last = render_start - self.last_render_time
                if time_since_last < (1.0 / self.render_fps_target):
                    sleep_time = max(0, (1.0/self.render_fps_target) - time_since_last)
                    time.sleep(sleep_time)
                    continue
                
                # Use frame from inference pipeline if available (eliminates duplicate camera reads)
                if self.current_frame is not None:
                    self._update_video_display(self.current_frame)
                    self.current_frame = None  # Clear processed frame
                else:
                    # Fallback: read directly from camera if no pipeline frame available
                    if self.camera_adapter and self.camera_adapter.is_opened():
                        frame = self.camera_adapter.read_frame()
                        if frame is not None:
                            self._update_video_display(frame)
                
                self.last_render_time = render_start
                
            except Exception as e:
                logger.error(f"Video update error: {e}")
                break
    
    def _update_video_display(self, frame: np.ndarray) -> None:
        """Update video surface with simplified CPU-only rendering to avoid base64 issues."""
        try:
            # Comprehensive component validation
            if not self._validate_rendering_components():
                logger.debug("Component validation failed")
                return
            
            # Enhanced frame validation with detailed logging
            if not self._validate_frame(frame):
                logger.debug("Frame validation failed")
                return
            
            logger.debug(f"Starting video display update - using CPU-only rendering")
            
            # ONLY use CPU fallback - completely bypass VideoSurface encoding
            success = self._render_direct_cpu(frame)
            logger.debug(f"Direct CPU render result: {success}")
            
            # Render transparent overlays after base video is established
            if success and self.is_running:
                logger.debug("Rendering transparent overlays")
                self._render_transparent_overlays()
            
            # Safe page update with comprehensive error handling
            if success and self.page and self.is_running:
                try:
                    self.page.update()
                    logger.debug("Page updated successfully")
                except Exception as e:
                    logger.debug(f"Page update deferred during shutdown: {e}")
            
        except Exception as e:
            logger.error(f"Video display update failed: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
    
    def _validate_rendering_components(self) -> bool:
        """Validate all rendering components are ready."""
        if not self.video_surface:
            logger.debug("Video surface not initialized")
            return False
        if not self.page:
            logger.debug("Page not available")
            return False
        if not self.video_surface.image_control:
            logger.debug("Image control not available")
            return False
        # Note: Removed is_running check to allow preview rendering when assessment is not active
        return True
    
    def _validate_frame(self, frame: np.ndarray) -> bool:
        """Enhanced frame validation with detailed checks."""
        if frame is None:
            logger.debug("Frame is None")
            return False
        if frame.size == 0:
            logger.debug("Frame is empty")
            return False
        if len(frame.shape) < 2:
            logger.debug(f"Invalid frame dimensions: {frame.shape}")
            return False
        if frame.shape[0] == 0 or frame.shape[1] == 0:
            logger.debug(f"Zero width/height frame: {frame.shape}")
            return False
        return True
    
    def _preprocess_frame_for_gpu(self, frame: np.ndarray) -> Optional[np.ndarray]:
        """Preprocess frame for GPU rendering with transparency support."""
        try:
            # Ensure frame is in correct format
            if len(frame.shape) == 3:
                if frame.shape[2] == 4:
                    # RGBA frame - convert to RGB to avoid transparency conflicts
                    frame = cv2.cvtColor(frame, cv2.COLOR_RGBA2RGB)
                elif frame.shape[2] == 3:
                    # RGB or BGR frame - ensure it's RGB for consistency
                    if frame.dtype != np.uint8:
                        frame = frame.astype(np.uint8)
                else:
                    logger.warning(f"Unexpected channel count: {frame.shape[2]}")
                    return None
            elif len(frame.shape) == 2:
                # Grayscale - convert to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2RGB)
            else:
                logger.warning(f"Invalid frame shape: {frame.shape}")
                return None
            
            # Resize frame efficiently for GPU processing
            target_size = (self.video_surface.width, self.video_surface.height)
            if frame.shape[:2][::-1] != target_size:
                frame = cv2.resize(frame, target_size, interpolation=cv2.INTER_AREA)
            
            return frame
            
        except Exception as e:
            logger.debug(f"Frame preprocessing failed: {e}")
            return None
    
    def _render_direct_cpu(self, frame: np.ndarray) -> bool:
        """Direct CPU rendering with minimal processing to avoid encoding conflicts."""
        try:
            logger.debug("Starting direct CPU rendering")
            
            # Simple frame validation
            if frame is None or frame.size == 0:
                logger.debug("Invalid frame")
                return False
            
            logger.debug(f"Direct CPU - input frame: shape={frame.shape}, dtype={frame.dtype}")
            
            # Minimal frame processing
            processed_frame = frame.copy()
            
            # Convert to BGR if needed (OpenCV standard)
            if len(processed_frame.shape) == 3 and processed_frame.shape[2] == 3:
                # Assume input is RGB from camera, convert to BGR for OpenCV
                processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_RGB2BGR)
            elif len(processed_frame.shape) == 2:
                # Grayscale to BGR
                processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
            
            # Resize to target
            target_size = (640, 480)  # Fixed size to avoid conflicts
            processed_frame = cv2.resize(processed_frame, target_size, interpolation=cv2.INTER_LINEAR)
            
            logger.debug(f"Processed frame ready: shape={processed_frame.shape}")
            
            # Use JPEG with minimal compression for better compatibility
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]  # High quality
            success_encode, buffer = cv2.imencode('.jpg', processed_frame, encode_params)

            if success_encode and buffer is not None and len(buffer) > 0:
                try:
                    # Ensure buffer is valid numpy array
                    if not isinstance(buffer, np.ndarray):
                        logger.error("Invalid buffer type")
                        return False

                    # Convert to bytes properly
                    buffer_bytes = buffer.tobytes()

                    # Validate buffer size
                    if len(buffer_bytes) < 1000:  # Minimum reasonable image size
                        logger.error(f"Buffer too small: {len(buffer_bytes)} bytes")
                        return False

                    # Clean base64 encoding with proper error handling
                    try:
                        frame_base64 = base64.b64encode(buffer_bytes).decode('utf-8')
                    except Exception as b64_error:
                        logger.error(f"Base64 encoding failed: {b64_error}")
                        return False

                    # Validate base64 string
                    if not frame_base64 or len(frame_base64) < 100:
                        logger.error(f"Invalid base64 length: {len(frame_base64) if frame_base64 else 0}")
                        return False

                    # Simple validation - just check if it's not empty
                    if not frame_base64 or len(frame_base64) < 10:
                        logger.error("Base64 string too short or empty")
                        return False

                    # Debug: Check first few characters of base64
                    logger.debug(f"Base64 first 50 chars: {frame_base64[:50]}")
                    logger.debug(f"Base64 length: {len(frame_base64)}")

                    # Create clean data URL for JPEG
                    data_url = f"data:image/jpeg;base64,{frame_base64}"

                    # Direct assignment with error handling - try different approaches
                    if self.video_surface and self.video_surface.image_control:
                        try:
                            # Method 1: Try with proper data URL format
                            try:
                                self.video_surface.image_control.src_base64 = data_url
                                logger.debug("Method 1 (data URL) successful")
                            except Exception as e1:
                                logger.debug(f"Method 1 failed: {e1}")
                                # Method 2: Try with just base64
                                try:
                                    self.video_surface.image_control.src_base64 = frame_base64
                                    logger.debug("Method 2 (raw base64) successful")
                                except Exception as e2:
                                    logger.debug(f"Method 2 failed: {e2}")
                                    # Method 3: Try setting src instead of src_base64
                                    try:
                                        self.video_surface.image_control.src = data_url
                                        logger.debug("Method 3 (src with data URL) successful")
                                    except Exception as e3:
                                        logger.error(f"All methods failed: {e1}, {e2}, {e3}")
                                        return False

                            # Throttled UI update for video preview
                            current_time = time.time()
                            if self.page and (current_time - self.last_ui_update) >= self.ui_update_interval:
                                try:
                                    self.page.update()
                                    self.last_ui_update = current_time
                                except Exception as update_error:
                                    logger.debug(f"UI update skipped: {update_error}")

                            logger.debug("Direct CPU rendering successful")
                            return True
                        except Exception as flet_error:
                            logger.error(f"Flet base64 assignment error: {flet_error}")
                            return False
                    else:
                        logger.error("Video surface or image control not available")
                        return False

                except Exception as e:
                    logger.error(f"Direct encoding error: {e}")
                    return False
            
            logger.error("Direct CPU encoding failed")
            return False

        except Exception as e:
            logger.error(f"Direct CPU rendering failed: {e}")
            # Try fallback with simpler encoding
            try:
                return self._fallback_simple_rendering(frame)
            except Exception as fallback_error:
                logger.error(f"Fallback rendering also failed: {fallback_error}")
                return False

    def _fallback_simple_rendering(self, frame) -> bool:
        """Simple fallback rendering with minimal processing."""
        try:
            if frame is None:
                return False

            # Very simple processing - just resize
            simple_frame = cv2.resize(frame, (320, 240), interpolation=cv2.INTER_NEAREST)

            # Use JPEG for better compatibility
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 85]  # Good quality for fallback
            success, buffer = cv2.imencode('.jpg', simple_frame, encode_params)

            if success and buffer is not None:
                try:
                    # Simple base64 without validation
                    frame_base64 = base64.b64encode(buffer.tobytes()).decode('utf-8')
                    data_url = f"data:image/jpeg;base64,{frame_base64}"

                    if self.video_surface and self.video_surface.image_control:
                        try:
                            # Try setting just the base64 string without data URL prefix
                            self.video_surface.image_control.src_base64 = frame_base64

                            # Throttled UI update for fallback rendering
                            current_time = time.time()
                            if self.page and (current_time - self.last_ui_update) >= self.ui_update_interval:
                                try:
                                    self.page.update()
                                    self.last_ui_update = current_time
                                except Exception:
                                    pass  # Ignore update errors in fallback

                            logger.debug("Fallback rendering successful")
                            return True
                        except Exception as flet_error:
                            logger.error(f"Flet fallback base64 assignment error: {flet_error}")
                            return False
                except Exception:
                    pass

            return False
        except Exception:
            return False

    def _render_transparent_overlays(self) -> None:
        """Render transparent overlays (skeleton, heatmap) over video."""
        try:
            # Render skeleton overlay with transparency if keypoints available
            if (self.current_keypoints and
                self.skeleton_renderer and
                hasattr(self.skeleton_renderer, 'draw_transparent')):
                try:
                    self.skeleton_renderer.draw_transparent(
                        self.current_keypoints,
                        self.current_risk_level
                    )
                except Exception as e:
                    logger.debug(f"Transparent skeleton render failed: {e}")
                    # Fallback to standard skeleton rendering
                    if hasattr(self.skeleton_renderer, 'draw'):
                        self.skeleton_renderer.draw(self.current_keypoints, self.current_risk_level)
            
            # Render heatmap overlay if enabled
            if (self.heatmap_checkbox and
                self.heatmap_checkbox.value and
                self.heatmap_layer):
                try:
                    self.heatmap_layer.render_transparent()
                except Exception as e:
                    logger.debug(f"Transparent heatmap render failed: {e}")
                    # Fallback to standard heatmap
                    try:
                        self.heatmap_layer.render_stub()
                    except Exception:
                        pass  # Ignore heatmap failures
            
        except Exception as e:
            logger.debug(f"Overlay rendering failed: {e}")
    
    def _attempt_rendering_recovery(self) -> None:
        """Attempt to recover from rendering failures."""
        try:
            # Reset video surface if needed
            if self.video_surface and hasattr(self.video_surface, 'reset'):
                self.video_surface.reset()
            
            # Clear any stuck overlays
            if self.skeleton_renderer and hasattr(self.skeleton_renderer, 'clear'):
                self.skeleton_renderer.clear()
            
            # Reset GPU state if needed
            if self.gpu_enabled:
                logger.info("Attempting GPU reset for recovery")
                # Temporarily disable GPU to clear state
                original_gpu_state = self.gpu_enabled
                self.gpu_enabled = False
                time.sleep(0.1)  # Brief pause
                self.gpu_enabled = original_gpu_state
            
        except Exception as e:
            logger.debug(f"Recovery attempt failed: {e}")
    
    def _on_heatmap_toggle(self, e) -> None:
        """Handle heatmap checkbox toggle."""
        try:
            if not self.heatmap_layer:
                return
            
            is_enabled = e.control.value
            self.heatmap_layer.set_enabled(is_enabled)
            
            if is_enabled:
                # Show stub heatmap overlay
                self.heatmap_layer.render_stub()
            else:
                # Clear heatmap
                self.heatmap_layer.clear_heatmap()
            
            # Update page to reflect changes
            if self.page:
                self.page.update()
            
            logger.info(f"Heatmap {'enabled' if is_enabled else 'disabled'}")
            
        except Exception as e:
            logger.error(f"Failed to toggle heatmap: {e}")
    
    def _update_scores_display(self, scores: ErgonomicScore, quality: float) -> None:
        """Update score display in UI."""
        try:
            # Check if page and components are still valid
            if not self.page or not self.reba_score_text or not self.rula_score_text:
                return
                
            # Update score texts
            reba_risk = scores.get_reba_risk_level()
            rula_risk = scores.get_rula_risk_level()
            
            self.reba_score_text.value = f"REBA: {scores.reba_score:.1f} ({reba_risk.value.upper()})"
            self.rula_score_text.value = f"RULA: {scores.rula_score:.1f} ({rula_risk.value.upper()})"
            self.quality_text.value = f"Kualitas Pose: {quality:.1%}"
            
            # Color code based on risk levels
            self.reba_score_text.color = self._get_risk_color(reba_risk)
            self.rula_score_text.color = self._get_risk_color(rula_risk)
            
            # Safe UI update
            try:
                self.page.update()
            except Exception as update_error:
                logger.debug(f"UI update skipped during shutdown: {update_error}")
            
        except Exception as e:
            logger.debug(f"Score display update skipped: {e}")
    
    def _get_risk_color(self, risk_level) -> str:
        """Get color for risk level display."""
        color_map = {
            'low': ft.Colors.GREEN,
            'medium': ft.Colors.YELLOW,
            'high': ft.Colors.ORANGE,
            'critical': ft.Colors.RED,
        }
        return color_map.get(risk_level.value, ft.Colors.GREY)
    
    def _handle_camera_error(self, error_message: str) -> None:
        """Handle camera error events."""
        self.status_text.value = f"Error kamera: {error_message}"
        self.status_text.color = ft.Colors.RED
        if self.page:
            try:
                self.page.update()
            except Exception as e:
                logger.debug(f"UI update error during camera error: {e}")
    
    def _update_performance_display(self, perf_event: PerformanceStatsEvent) -> None:
        """Update performance display with separate FPS metrics."""
        try:
            # Check if page and components are still valid
            if not self.page or not self.capture_fps_text or not self.performance_status_text:
                return
                
            # Extract separate FPS metrics from metadata if available
            capture_fps = perf_event.metadata.get('capture_fps', perf_event.fps) if perf_event.metadata else perf_event.fps
            infer_fps = perf_event.metadata.get('infer_fps', perf_event.avg_fps) if perf_event.metadata else perf_event.avg_fps
            render_fps = perf_event.metadata.get('render_fps', self.render_fps_target) if perf_event.metadata else self.render_fps_target
            
            # Update separate FPS displays
            self.capture_fps_text.value = f"Capture FPS: {capture_fps:.1f}"
            self.infer_fps_text.value = f"Infer FPS: {infer_fps:.1f}"
            self.render_fps_text.value = f"Render FPS: {render_fps:.1f}"
            
            # Update latency display
            self.latency_text.value = f"Latency: {perf_event.frame_latency:.1f}ms"
            
            # Update performance status with color coding
            target_fps = 25.0  # Expected target
            min_fps = min(capture_fps, infer_fps, render_fps)
            performance_ratio = min_fps / target_fps if target_fps > 0 else 0.0
            
            if performance_ratio >= 1.0:
                status = "Sangat Baik"
                status_color = ft.Colors.GREEN
            elif performance_ratio >= 0.8:
                status = "Baik"
                status_color = ft.Colors.BLUE
            elif performance_ratio >= 0.6:
                status = "Cukup"
                status_color = ft.Colors.ORANGE
            else:
                status = "Kurang"
                status_color = ft.Colors.RED
            
            self.performance_status_text.value = f"Performa: {status} ({performance_ratio:.1%})"
            self.performance_status_text.color = status_color
            
            # Color FPS texts based on performance targets
            for fps_text, fps_value in [
                (self.capture_fps_text, capture_fps),
                (self.infer_fps_text, infer_fps),
                (self.render_fps_text, render_fps)
            ]:
                if fps_text and fps_value is not None:
                    if fps_value >= target_fps * 0.9:
                        fps_text.color = ft.Colors.GREEN
                    elif fps_value >= target_fps * 0.7:
                        fps_text.color = ft.Colors.ORANGE
                    else:
                        fps_text.color = ft.Colors.RED
            
            # Safe UI update
            try:
                self.page.update()
            except Exception as update_error:
                logger.debug(f"Performance UI update skipped during shutdown: {update_error}")
            
        except Exception as e:
            logger.debug(f"Performance display update skipped: {e}")
    
    def _on_start_session_clicked(self, e) -> None:
        """Handle start session button click."""
        try:
            session_name = self.session_name_field.value.strip()
            if not session_name:
                session_name = f"Session {time.strftime('%Y-%m-%d %H:%M')}"
            
            session = self.session_manager.start_session(
                session_name=session_name,
                camera_id="default",
                assessment_type="continuous"
            )
            
            self.session_status_text.value = f"Active: {session.session_name}"
            self.session_status_text.color = ft.Colors.GREEN
            
            logger.info(f"Started session: {session.session_id}")
            self.page.update()
            
        except ValueError as ve:
            self.session_status_text.value = f"Error: {str(ve)}"
            self.session_status_text.color = ft.Colors.RED
            self.page.update()
        except Exception as ex:
            logger.error(f"Failed to start session: {ex}")
            self.session_status_text.value = "Gagal memulai sesi"
            self.session_status_text.color = ft.Colors.RED
            self.page.update()
    
    def _on_stop_session_clicked(self, e) -> None:
        """Handle stop session button click."""
        try:
            # Auto-stop assessment first if running to avoid UI update conflicts
            if self.is_running:
                self.is_running = False  # Set flag first to stop video updates
                if self.capture_use_case:
                    self.capture_use_case.stop()
                if self.recommendation_use_case:
                    self.recommendation_use_case.stop()
                    
                # Update UI states safely
                if self.start_button:
                    self.start_button.disabled = False
                if self.stop_button:
                    self.stop_button.disabled = True
                if self.status_text:
                    self.status_text.value = "Asesmen dihentikan"
                    self.status_text.color = ft.Colors.GREY
            
            # Stop session
            stopped_session = self.session_manager.stop_session()
            
            if stopped_session:
                if self.session_status_text:
                    self.session_status_text.value = f"Completed: {stopped_session.session_name}"
                    self.session_status_text.color = ft.Colors.GREY
                
                logger.info(f"Stopped session: {stopped_session.session_id}")
            else:
                if self.session_status_text:
                    self.session_status_text.value = "Tidak ada sesi aktif untuk dihentikan"
                    self.session_status_text.color = ft.Colors.ORANGE
            
            # Safe UI update
            if self.page:
                try:
                    self.page.update()
                except Exception as update_error:
                    logger.debug(f"UI update during session stop failed: {update_error}")
            
        except Exception as ex:
            logger.debug(f"Session stop failed: {ex}")
            if self.session_status_text:
                self.session_status_text.value = "Gagal menghentikan sesi"
                self.session_status_text.color = ft.Colors.RED
            # Try one more safe update
            if self.page:
                try:
                    self.page.update()
                except Exception:
                    pass  # Ignore if page is shutting down
    
    def _on_session_data_updated(self, session) -> None:
        """Callback for session data updates."""
        try:
            # This callback is called when session data changes
            # Can be used for real-time UI updates
            pass
        except Exception as e:
            logger.debug(f"Session data callback failed: {e}")
    
    def _start_video_preview(self) -> None:
        """Start video preview to show camera feed before assessment."""
        try:
            if self.camera_adapter and self.camera_adapter.is_opened():
                self.status_text.value = "Video preview aktif"
                self.status_text.color = ft.Colors.GREEN
                if self.page:
                    self.page.update()
                
                # Start preview thread
                threading.Thread(target=self._preview_loop, daemon=True).start()
                logger.info("Video preview started")
            else:
                self.status_text.value = "Kamera tidak tersedia untuk preview"
                self.status_text.color = ft.Colors.ORANGE
                if self.page:
                    self.page.update()
        except Exception as e:
            logger.error(f"Failed to start video preview: {e}")
    
    def _preview_loop(self) -> None:
        """Preview loop to show video feed when not running assessment."""
        preview_fps = 15.0  # Lower FPS for preview
        frame_interval = 1.0 / preview_fps
        last_frame_time = 0
        
        while not self.is_running:
            try:
                current_time = time.time()
                
                # Throttle preview FPS
                if current_time - last_frame_time < frame_interval:
                    time.sleep(0.01)
                    continue
                
                # Read frame from camera for preview
                if self.camera_adapter and self.camera_adapter.is_opened():
                    frame = self.camera_adapter.read_frame()
                    if frame is not None:
                        self._update_video_display(frame)
                    else:
                        # Camera might be disconnected
                        break
                else:
                    break
                
                last_frame_time = current_time
                
            except Exception as e:
                logger.debug(f"Preview loop error: {e}")
                break
        
        logger.debug("Video preview loop ended")


def main() -> None:
    """Application entry point function.
    
    This function is called when the application is launched directly
    or through the package entry point.
    """
    try:
        app = ErgonomicsApp()
        
        # TODO-CODE: Add command line argument parsing
        # TODO-CODE: Support for different deployment modes (desktop, mobile)
        # TODO-CODE: Add configuration file loading
        
        # Launch Flet application
        ft.app(
            target=app.main,
            name="Asesmen Ergonomik",
            assets_dir="assets"  # TODO-CODE: Create assets directory
        )
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()