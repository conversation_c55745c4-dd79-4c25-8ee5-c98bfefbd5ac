# Base64 Data URI Fix

## Problem Description

You were experiencing a base64 decoding error with the message:

```
Error decoding base64: FormatException: Invalid character (at character 5)
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAEvklEQ...
    ^
```

### Root Cause

The error occurs because:

1. **Data URI Format**: The string `"data:image/png;base64,iVBORw0KGgoAAAA..."` is a data URI, not raw base64
2. **Character 5 Issue**: Character 5 is the colon `:` in "data:", which is not a valid base64 character
3. **Incorrect Processing**: The base64 decoder was trying to decode the entire data URI string instead of just the base64 content after the comma

### Data URI Structure

```
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAEvklEQ...
│    │         │       │
│    │         │       └─ Base64 content (this part should be decoded)
│    │         └─ Encoding type
│    └─ Media type
└─ Data URI prefix
```

## Solution

I've created a comprehensive fix with three components:

### 1. Data URI Utilities (`src/infrastructure/utils/data_uri_utils.py`)

Utility functions to properly handle data URIs:

- `parse_data_uri()` - Parse data URI into components
- `extract_base64_from_data_uri()` - Extract only the base64 content
- `safe_base64_decode()` - Safely decode both data URIs and raw base64
- `validate_data_uri()` - Validate data URI format

### 2. Custom Pydantic Types (`src/infrastructure/utils/pydantic_types.py`)

Drop-in replacements for Pydantic's base64 types:

- `DataUriBase64String` - Handles data URIs, returns base64 string
- `DataUriBase64BytesType` - Handles data URIs, returns decoded bytes

### 3. Test Script (`test_base64_fix.py`)

Demonstrates the fix and verifies it works correctly.

## How to Use the Fix

### Option 1: Use Utility Functions

```python
from src.infrastructure.utils.data_uri_utils import safe_base64_decode

# This will work with both data URIs and raw base64
data_uri = "data:image/png;base64,iVBORw0KGgoAAAA..."
decoded_bytes = safe_base64_decode(data_uri)

if decoded_bytes:
    print(f"Successfully decoded {len(decoded_bytes)} bytes")
else:
    print("Failed to decode")
```

### Option 2: Use Custom Pydantic Types

Replace your existing Pydantic models:

```python
# OLD (causes the error):
from pydantic import BaseModel
from pydantic.types import Base64Str, Base64Bytes

class MyModel(BaseModel):
    image_data: Base64Str  # Fails with data URIs

# NEW (works with data URIs):
from pydantic import BaseModel
from src.infrastructure.utils.pydantic_types import DataUriBase64String, DataUriBase64BytesType

class MyModel(BaseModel):
    image_data: DataUriBase64String  # Works with both data URIs and raw base64
    image_bytes: DataUriBase64BytesType  # Returns decoded bytes
```

### Option 3: Pre-process Data URIs

If you can't change your models, pre-process the data:

```python
from src.infrastructure.utils.data_uri_utils import extract_base64_from_data_uri

data_uri = "data:image/png;base64,iVBORw0KGgoAAAA..."
base64_content = extract_base64_from_data_uri(data_uri)

if base64_content:
    # Now you can use the base64 content with standard Pydantic types
    model = MyModel(image_data=base64_content)
```

## Testing the Fix

Run the test script to verify everything works:

```bash
python test_base64_fix.py
```

This will:
1. Demonstrate the original problem
2. Test the utility functions
3. Test the new Pydantic types
4. Show how to handle your specific error case

## Key Features

✅ **Backward Compatible**: Works with both data URIs and raw base64 strings
✅ **Drop-in Replacement**: Can replace existing Pydantic Base64 types
✅ **Proper Error Handling**: Clear error messages for invalid input
✅ **Validation**: Ensures base64 content is valid before processing
✅ **Type Safety**: Full Pydantic integration with proper type hints

## Example Usage in Your Code

If you have existing code that's failing:

```python
# If this is failing:
try:
    model = SomeModel(image_field="data:image/png;base64,iVBORw0...")
except ValidationError as e:
    print(f"Error: {e}")  # "Invalid character (at character 5)"

# Fix it by changing the model definition:
from src.infrastructure.utils.pydantic_types import DataUriBase64String

class SomeModel(BaseModel):
    image_field: DataUriBase64String  # Now it works!

# Or pre-process the data:
from src.infrastructure.utils.data_uri_utils import extract_base64_from_data_uri

data_uri = "data:image/png;base64,iVBORw0..."
base64_content = extract_base64_from_data_uri(data_uri)
model = SomeModel(image_field=base64_content)
```

## Integration

To integrate this fix into your project:

1. **Import the utilities** where you need them
2. **Replace problematic Pydantic types** with the new ones
3. **Update your models** to use `DataUriBase64String` or `DataUriBase64BytesType`
4. **Test with your actual data** to ensure it works

The fix is designed to be minimally invasive and should work with your existing codebase with minimal changes.
