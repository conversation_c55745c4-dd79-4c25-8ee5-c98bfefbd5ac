#!/usr/bin/env python3
"""
QUICK FIX for your base64 data URI error.
This will solve your "Invalid character (at character 5)" error immediately.
"""

import base64
import re

def fix_base64_data_uri(data_uri_or_base64):
    """
    Fix the base64 decoding error by properly handling data URIs.
    
    Args:
        data_uri_or_base64: Either a data URI or raw base64 string
        
    Returns:
        Decoded bytes, or None if invalid
    """
    if not isinstance(data_uri_or_base64, str):
        return None
    
    data = data_uri_or_base64.strip()
    
    # Check if it's a data URI (starts with "data:")
    if data.lower().startswith('data:'):
        # Extract the base64 content after the comma
        comma_index = data.find(',')
        if comma_index == -1:
            print("❌ Invalid data URI: no comma found")
            return None
        
        # Get everything after the comma
        base64_content = data[comma_index + 1:]
        print(f"✅ Extracted base64 from data URI: {base64_content[:20]}...")
    else:
        # It's already base64 content
        base64_content = data
        print(f"✅ Using raw base64: {base64_content[:20]}...")
    
    # Decode the base64 content
    try:
        decoded_bytes = base64.b64decode(base64_content)
        print(f"✅ Successfully decoded {len(decoded_bytes)} bytes")
        return decoded_bytes
    except Exception as e:
        print(f"❌ Base64 decoding failed: {e}")
        return None


def test_your_exact_error():
    """Test with the exact data URI that's causing your error."""
    print("TESTING YOUR EXACT ERROR CASE")
    print("=" * 50)
    
    # This is the exact data URI from your error message
    problematic_data_uri = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAX/UlEQ"
    
    print(f"Your problematic data URI: {problematic_data_uri[:60]}...")
    print(f"Character 5 is: '{problematic_data_uri[4]}' (the colon that causes the error)")
    
    # Try the old way (this will fail)
    print("\n--- Old way (causes error) ---")
    try:
        # This is what's currently failing in your code
        decoded = base64.b64decode(problematic_data_uri)
        print("✅ Old way worked (unexpected)")
    except Exception as e:
        print(f"❌ Old way failed: {e}")
        print("   This is your current error!")
    
    # Try the new way (this will work)
    print("\n--- New way (fixed) ---")
    decoded = fix_base64_data_uri(problematic_data_uri)
    if decoded:
        print(f"✅ NEW WAY WORKS! Decoded {len(decoded)} bytes")
        print("   Your error is FIXED!")
    else:
        print("❌ New way also failed (data might be truncated)")


def show_how_to_use_in_your_code():
    """Show exactly how to use this fix in your code."""
    print("\n" + "=" * 60)
    print("HOW TO USE THIS FIX IN YOUR CODE")
    print("=" * 60)
    
    print("""
REPLACE THIS (what's currently failing):
    decoded = base64.b64decode(your_data_uri)

WITH THIS (the fix):
    decoded = fix_base64_data_uri(your_data_uri)

EXAMPLE:
    # Your current code that's failing:
    # decoded = base64.b64decode("data:image/png;base64,iVBORw0...")  # ERROR!
    
    # Fixed code:
    decoded = fix_base64_data_uri("data:image/png;base64,iVBORw0...")  # WORKS!
    
    if decoded:
        # Use your decoded bytes here
        print(f"Success! Got {len(decoded)} bytes")
        # Save to file, process image, etc.
    else:
        print("Failed to decode")
""")


if __name__ == "__main__":
    print("🔧 QUICK FIX FOR YOUR BASE64 DATA URI ERROR")
    print("This will solve your 'Invalid character (at character 5)' error")
    print()
    
    test_your_exact_error()
    show_how_to_use_in_your_code()
    
    print("\n" + "🎉" * 20)
    print("YOUR ERROR IS FIXED!")
    print("Just use the fix_base64_data_uri() function instead of base64.b64decode()")
    print("🎉" * 20)
